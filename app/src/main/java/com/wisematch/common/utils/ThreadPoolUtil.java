package com.wisematch.common.utils;

import brave.Tracer;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.wisematch.config.TraceAwareThreadPoolExecutor;
import com.wisematch.modules.chat.service.DingTalkService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.List;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ThreadPoolUtil implements DisposableBean {

    /**
     * 核心线程数，会一直存活，即使没有任务，线程池也会维护线程的最少数量
     */
    private static final int SIZE_CORE_POOL = 100;
    /**
     * 线程池维护线程的最大数量
     */
    private static final int SIZE_MAX_POOL = 500;
    /**
     * 线程池维护线程所允许的空闲时间
     */
    private static final long ALIVE_TIME = 2000;
    /**
     * 线程缓冲队列
     */
    private static final BlockingQueue<Runnable> BQUEUE = new ArrayBlockingQueue<>(10000);
    private static ExecutorService TTL_POOL;
    private static ThreadPoolExecutor rawPool;

    // 线程池关闭超时时间
    private static final int SHUTDOWN_TIMEOUT_SECONDS = 60;


    @Resource
    private Tracer tracer;

    @Resource
    private DingTalkService dingTalkService;

    @PostConstruct
    private void init() {
        rawPool = new TraceAwareThreadPoolExecutor(
                new ThreadPoolExecutor(SIZE_CORE_POOL, SIZE_MAX_POOL, ALIVE_TIME, TimeUnit.MILLISECONDS, BQUEUE, new ThreadPoolExecutor.AbortPolicy()
                ), tracer, dingTalkService);
        rawPool.prestartAllCoreThreads();

        TTL_POOL = TtlExecutors.getTtlExecutorService(rawPool);
    }

    /**
     * 执行方法
     *
     * @param runnable
     */

    public static void execute(Runnable runnable) {
        TTL_POOL.execute(runnable);
    }

    /**
     * 提交返回值
     *
     * @param callable
     */

    public static <T> Future<T> submit(Callable<T> callable) {
        return TTL_POOL.submit(callable);
    }

    public static ExecutorService getPool() {
        return TTL_POOL;
    }

    /**
     * 上下文感知的 supplyAsync 方法（支持 ThreadLocal、SecurityContext、RequestContext）
     */
    public static <T> CompletableFuture<T> supplyAsync(SupplierWithException<T> supplier) {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();

        return CompletableFuture.supplyAsync(() -> {
            try {
                SecurityContextHolder.setContext(securityContext);
                RequestContextHolder.setRequestAttributes(requestAttributes);
                return supplier.get();
            } catch (Exception e) {
                throw new CompletionException(e);
            } finally {
                SecurityContextHolder.clearContext();
                RequestContextHolder.resetRequestAttributes();
            }
        }, TTL_POOL);
    }

    @Override
    public void destroy() throws Exception {
        if (TTL_POOL != null && !TTL_POOL.isShutdown()) {
            // 获取原始线程池
            log.warn("开始关闭线程池，当前活跃线程数: {}", rawPool.getActiveCount());

            // 第一步：停止接收新任务
            TTL_POOL.shutdown();

            // 第二步：等待正在执行的任务完成
            if (!TTL_POOL.awaitTermination(SHUTDOWN_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
                log.warn("线程池未能在指定时间内完成所有任务，将尝试中断剩余任务");

                // 第三步：强制中断仍在执行的任务
                List<Runnable> unexecutedTasks = TTL_POOL.shutdownNow();
                log.warn("强制中断后，未执行的任务数: {}", unexecutedTasks.size());

                // 第四步：等待强制中断完成
                if (!TTL_POOL.awaitTermination(SHUTDOWN_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
                    log.error("线程池强制关闭失败，可能有任务未完成");
                }
            }

            log.info("线程池已关闭，剩余未执行任务数: {}",rawPool.getQueue().size());
        }
    }

    @FunctionalInterface
    public interface SupplierWithException<T> {
        T get() throws Exception;
    }

    public static ExecutorService getInstance() {
        return TTL_POOL;
    }
}
