package com.wisematch.modules.exam.controller;

import com.wisematch.common.annotation.SysLog;
import com.wisematch.common.model.PageDTO;
import com.wisematch.common.utils.R;
import com.wisematch.modules.chat.model.IdRequest;
import com.wisematch.modules.exam.service.AiAnswerQuestionService;
import com.wisematch.modules.exam.service.AiExamQuestionRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version AiMatcherController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/exam/user")
@Tag(name = "用户试卷", description = "用户试卷")
public class AiExamUserController {


    @Autowired
    private AiAnswerQuestionService aiAnswerQuestionService;

    @Operation(summary = "用户测试记录")
    @PostMapping("/userMbtiList")
    public R userMbtiList(@RequestBody PageDTO pageDTO) {
        //应该是只查最新的一次做题记录
        return R.ok().setData(aiAnswerQuestionService.userExamList(pageDTO));
    }

    @Operation(summary = "查看报告")
    @PostMapping("/mbtiReportCard")
    public R mbtiReportCard(@RequestBody IdRequest idRequest) {
        //应该是只查最新的一次做题记录
        return R.ok().setData(aiAnswerQuestionService.getReportCard(Long.valueOf(idRequest.getId())));
    }

}
