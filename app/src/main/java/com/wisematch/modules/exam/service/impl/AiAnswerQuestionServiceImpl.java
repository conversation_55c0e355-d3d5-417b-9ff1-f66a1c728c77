package com.wisematch.modules.exam.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wisematch.common.model.PageDTO;
import com.wisematch.common.utils.BeanCopyUtils;
import com.wisematch.common.utils.EnumUtil;
import com.wisematch.common.utils.PageConvertUtils;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.agent.MbtiJobMatchAgent;
import com.wisematch.modules.chat.entity.AiViewPortrait;
import com.wisematch.modules.chat.model.CardInfo;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.model.MbtiInfo;
import com.wisematch.modules.chat.service.AiViewPortraitService;
import com.wisematch.modules.common.service.AiSysConfigService;
import com.wisematch.modules.common.utils.JsonUtils;
import com.wisematch.modules.exam.entity.AiExamBank;
import com.wisematch.modules.exam.entity.AiExamQuestion;
import com.wisematch.modules.exam.entity.AiExamQuestionRecord;
import com.wisematch.modules.exam.entity.AiExamUser;
import com.wisematch.modules.exam.enums.AnserStatus;
import com.wisematch.modules.exam.enums.ExamUserStatus;
import com.wisematch.modules.exam.enums.StrategyType;
import com.wisematch.modules.exam.model.*;
import com.wisematch.modules.exam.reportGenerater.CombinationContext;
import com.wisematch.modules.exam.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.wisematch.modules.chat.enums.CardType.MBTI;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AiAnswerQuestionServiceImpl implements AiAnswerQuestionService {

    @Autowired
    private AiExamBankService aiExamBankService;
    @Autowired
    private AiSysConfigService aiSysConfigService;

    @Autowired
    private AiExamQuestionService aiExamQuestionService;
    @Autowired
    @Lazy
    private AiViewPortraitService aiViewPortraitService;

    @Autowired
    MbtiJobMatchAgent matchAgent;

    @Autowired
    AiExamQuestionRecordService aiExamQuestionRecordService;

    @Autowired
    private AiExamUserService aiExamUserService;

    private CardInfo cardWrapper(CardInfo e, String userId) {

        JSONObject extra = e.getExtra();
        String paperId = e.getId();
        AiExamUser ownLatest = aiExamUserService.getOwnLatestByUserId(Long.valueOf(paperId), userId);
        extra.put("answeredNum", aiExamQuestionRecordService.answerNum(ownLatest, userId));
        extra.put("total", aiExamQuestionService.paperQuestionNum(ownLatest, Long.valueOf(paperId)));
        extra.put("paperId", null != ownLatest ? String.valueOf(ownLatest.getId()) : paperId);
        extra.put("bankId", e.getId());
        extra.put("answerStatus", aiExamUserService.isAnswered(Long.valueOf(e.getId()), userId));
        e.setExtra(extra);
        return e;
    }

    @Override
    public Page<CardInfo> userExamList(PageDTO pageDTO) {
        Page<AiExamUser> page = new Page<>(pageDTO.getPageNum(), pageDTO.getPageSize());
        Page<AiExamUser> aiExamUserPage = aiExamUserService.getLatestRecord(page, UserInfoUtils.getCurrentUserId());
        return PageConvertUtils.convert(
                aiExamUserPage,
                CardInfo.class,
                (source, dest) -> {
                    dest.setLogo(source.getLogo());
                    dest.setId(String.valueOf(source.getId()));
                    dest.setTitle(source.getName());
                    dest.setKeyLabel(source.getExamType());
                    JSONObject extra = dest.getExtra();
                    dest.setCardType(MBTI.name());
                    extra.put("answeredNum", aiExamQuestionRecordService
                            .answerNum(source.getId(), UserInfoUtils.getCurrentUserId()));
                    extra.put("total", source.getQuestionNum());
                    extra.put("answerStatus", source.getAnswerStatus());
                    extra.put("paperId", source.getId());
                    extra.put("bankId", source.getBankId());
                    dest.setExtra(extra);
                }
        );
    }

    @Override
    @Transactional
    public void answerQuestion(AnswerQuestionDTO dto) {

        AiExamQuestionRecord oneRecord = aiExamQuestionRecordService.
                getOneRecord(new GetOneRecordDTO(dto.getPaperId(),
                        dto.getQuestionId(), UserInfoUtils.getCurrentUserId()));
        LambdaUpdateWrapper<AiExamQuestionRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AiExamQuestionRecord::getUserId, UserInfoUtils.getCurrentUserId());
        updateWrapper.eq(AiExamQuestionRecord::getQuestionId, dto.getQuestionId());
        updateWrapper.set(AiExamQuestionRecord::getUserAnswer, dto.getUserAnswer());
        updateWrapper.set(AiExamQuestionRecord::getStatus, AnserStatus.ANSWERED);
        QuestionOptionDTO.addUpdateWrapper(updateWrapper, oneRecord, dto);

        aiExamQuestionRecordService.update(updateWrapper);
        generateMbtiReport(dto.getPaperId());
    }

    public void generateMbtiReport(Long paperId) {
        long answerNum = aiExamQuestionRecordService.answerNum(paperId, UserInfoUtils.getCurrentUserId());
        AiExamUser aiExamUser = aiExamUserService.getById(paperId);
        if (answerNum >= aiExamUser.getQuestionNum()) {
            aiExamUser.setAnswerStatus(ExamUserStatus.SUBMITTED.name());
//            aiExamUserService.updateById(aiExamUser);
            //生成报告
            MbtiInfo mbtiInfo = generateReport(aiExamUser);
            aiExamUser.setReportContent(JsonUtils.toJson(mbtiInfo));
//            aiExamUser.setAnswerStatus(ExamUserStatus.REPORTED.name());
            aiExamUserService.updateById(aiExamUser);
            List<AiViewPortrait> aiViewPortraits = aiViewPortraitService.getByUserId(UserInfoUtils.getCurrentUserId());
            aiViewPortraits.forEach(e -> {
                        MbtiInfo matchResult = matchAgent.matchResult(e, mbtiInfo);
                        e.setMbtiInfo(JsonUtils.toJson(matchResult));
                        aiViewPortraitService.updateById(e);
                    }
            );
        }
    }

    @Override
    public MbtiInfo generateReport(AiExamUser aiExamUser) {
        List<AiExamQuestionRecord> aiExamQuestionRecords = aiExamQuestionRecordService.examRecord(new ExamQuestionFrontDTO(aiExamUser.getId()));
        List<String> answer = aiExamQuestionRecords.stream().map(AiExamQuestionRecord::getCorrectAnswer).toList();
        // 使用枚举选择策略
        CombinationContext context = new CombinationContext(
                EnumUtil.getEnumByName(StrategyType.class, aiExamUser.getExamType()).getStrategy());
        CombinationResult combinationResult = context.executeStrategy(CombinationResult.combinations, answer);
        String mbtiSimple = aiSysConfigService.selectConfigByKey("mbti_simple");

        return CombinationResult.getMbtiInfo(combinationResult, mbtiSimple);
    }

    @Override
    @Transactional
    public AiExamUser examUserAdd(AiExamUserAddDTO dto) {
        //copy试卷和试题到用户表
        Long paperId = dto.getPaperId();
        AiExamUser unSubmittedOne = aiExamUserService.unSubmittedOne(UserInfoUtils.getCurrentUserId(), dto.getPaperId());
        if (null != unSubmittedOne) {
            return unSubmittedOne;
        }
        List<AiExamQuestion> aiExamQuestions = aiExamQuestionService.paperQuestion(paperId);

        AiExamUser aiExamUser = generateAiExamUser(dto.getPaperId(), aiExamQuestions.size());
        generateQuestion(aiExamQuestions, aiExamUser.getId());

        return aiExamUser;
    }

    private void generateQuestion(List<AiExamQuestion> aiExamQuestions, Long paperId) {

        List<AiExamQuestionRecord> aiExamQuestionRecords = BeanCopyUtils.copyList(aiExamQuestions, AiExamQuestionRecord.class);
        aiExamQuestionRecords.forEach(aiExamQuestionRecord -> {
            aiExamQuestionRecord.setQuestionId(aiExamQuestionRecord.getId());
            aiExamQuestionRecord.setId(null);
            aiExamQuestionRecord.setPaperId(paperId);
            aiExamQuestionRecord.setUserId(UserInfoUtils.getCurrentUserId());
            aiExamQuestionRecord.setUsername(UserInfoUtils.getUsername());
        });
        aiExamQuestionRecordService.saveBatch(aiExamQuestionRecords);
    }

    private AiExamUser generateAiExamUser(Long paperId, Integer questionNum) {

        AiExamBank aiExamBank = aiExamBankService.getById(paperId);
        AiExamUser aiExamUser = new AiExamUser();
        BeanCopyUtils.copyProperties(aiExamBank, aiExamUser);
        aiExamUser.setId(null);
        aiExamUser.setUserId(UserInfoUtils.getCurrentUserId());
        aiExamUser.setBankId(aiExamBank.getId());
        aiExamUser.setQuestionNum(questionNum);
        aiExamUser.setAnswerStatus(ExamUserStatus.INIT.name());
        aiExamUser.setCreateTime(new Date());
        aiExamUserService.save(aiExamUser);
        return aiExamUser;
    }

    @Override
    public Page<CardInfo> frontBankPage(AiExamBankFrontDTO queryDTO) {
        Page<CardInfo> cardInfoPage = aiExamBankService.frontBankPage(queryDTO);

        PageConvertUtils.mapRecords(cardInfoPage, e -> {
            cardWrapper(e, UserInfoUtils.getCurrentUserId());
        });
        return cardInfoPage;
    }

    @Override
    public ChatMessage wrapWiserChatMessage(ChatMessage chatMessage) {

        List<? extends CardInfo> wildcardList = chatMessage.getCardInfos();

        if (wildcardList != null) {
            List<CardInfo> cardInfos = new ArrayList<>(wildcardList);
            chatMessage.setCardInfos(cardWrapper(cardInfos, UserInfoUtils.getCurrentUserId()));
        }
        return chatMessage;
    }

    @Override
    public MbtiInfo getReport(Long id) {
        AiExamUser aiExamUser = aiExamUserService.getById(id);
        return MbtiInfo.convertMbti(aiExamUser);
    }

    @Override
    public CardInfo getReportCard(Long id) {
        MbtiInfo report = getReport(id);
        return CardInfo.mbtiReportToCard(report);
    }


    @Override
    public MbtiInfo getLatestReport(String userId) {
        AiExamUser ownLatest = aiExamUserService.getOwnLatestByUserId(userId);
        //TODO 待删除
        if (ownLatest == null) {
            return MbtiInfo.mock();
        }
        return MbtiInfo.convertMbti(ownLatest);
    }

    @Override
    public List<CardInfo> cardWrapper(List<CardInfo> aiExamBanks, String userId) {
        return BeanCopyUtils.copyList(aiExamBanks,
                e -> {
                    if (e.getCardType().equals(MBTI.name())) {
                        return cardWrapper(e, userId);
                    }
                    return e;
                });
    }

}

