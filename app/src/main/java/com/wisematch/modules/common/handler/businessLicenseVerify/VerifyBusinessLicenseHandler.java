package com.wisematch.modules.common.handler.businessLicenseVerify;

import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.cloudauth20221125.models.EntElementVerifyV2Request;
import com.aliyun.sdk.service.cloudauth20221125.models.EntElementVerifyV2Response;
import com.aliyun.sdk.service.cloudauth20221125.models.EntElementVerifyV2ResponseBody;
import com.aliyun.sdk.service.ocr_api20210707.AsyncClient;
import com.aliyun.sdk.service.ocr_api20210707.models.VerifyBusinessLicenseRequest;
import com.aliyun.sdk.service.ocr_api20210707.models.VerifyBusinessLicenseResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.common.utils.ConfigConstant;
import com.wisematch.modules.chat.enums.WiserConstant;
import com.wisematch.modules.chat.model.AliCommonConfigDTO;
import com.wisematch.modules.chat.model.CertifiedEnterpriseDTO;
import com.wisematch.modules.chat.model.VerifyBusinessLicenseDTO;
import com.wisematch.modules.chat.model.VerifyBusinessLicenseVO;
import com.wisematch.modules.common.service.AiSysConfigService;
import com.wisematch.modules.common.utils.JsonUtils;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@Component
@Slf4j
public class VerifyBusinessLicenseHandler {


    @Autowired
    AiSysConfigService aiSysConfigService;


    public Integer verifyFourElements(CertifiedEnterpriseDTO certifiedEnterpriseDTO) throws ExecutionException, InterruptedException, JsonProcessingException {
        String aliVo = aiSysConfigService.selectConfigByKey("sys.agent.aliCommon");
        AliCommonConfigDTO aliCommonConfigDTO = JsonUtils.fromJson(aliVo, AliCommonConfigDTO.class);

        String config = aiSysConfigService.selectConfigByKey("sys.agent.VerifyBusinessLicense");
        VerifyBusinessLicenseDTO verifyBusinessLicenseDTO = JsonUtils.fromJson(config, VerifyBusinessLicenseDTO.class);

        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(aliCommonConfigDTO.getKeyId())
                .accessKeySecret(aliCommonConfigDTO.getSecretKey())
                .build());

        // Configure the Client
        com.aliyun.sdk.service.cloudauth20221125.AsyncClient client = com.aliyun.sdk.service.cloudauth20221125.AsyncClient.builder()
                .region(verifyBusinessLicenseDTO.getRegion())
                .credentialsProvider(provider)
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                .setEndpointOverride(verifyBusinessLicenseDTO.getEndpointOverride())
                )
                .build();

        EntElementVerifyV2Request verifyBusinessLicenseRequest = EntElementVerifyV2Request.builder()
                .userAuthorization("1")
                .sceneCode(ConfigConstant.IdUtils.fastSimpleUUID())
                .merchantBizId("orgVerify_"+ConfigConstant.IdUtils.fastSimpleUUID())
                .merchantUserId("mch_"+ConfigConstant.IdUtils.fastSimpleUUID())
                .infoVerifyType("ENT_4META")
                .licenseNo(certifiedEnterpriseDTO.getUniqueSocialCreditCode())
                .legalPersonName(certifiedEnterpriseDTO.getLegalPerson())
                .entName(certifiedEnterpriseDTO.getOrganizationName())
                .legalPersonCertNo(certifiedEnterpriseDTO.getLegalPersonIdCard())
                .build();
        try {
            CompletableFuture<EntElementVerifyV2Response> response = client.entElementVerifyV2(verifyBusinessLicenseRequest);

            EntElementVerifyV2Response resp = response.get();

            if(200 != resp.getStatusCode()){
                throw new RRException(RRExceptionEnum.SERVICE_UNAVAILABLE);
            }
            EntElementVerifyV2ResponseBody body = resp.getBody();
            String bizCode = body.getResult().getBizCode();
            String status = body.getResult().getStatus();
            if("1".equals(bizCode)&& "1".equals(status)){
                return WiserConstant.CERTIFIED;
            }
            return WiserConstant.CERTIFY_FAIL;
        }catch (Exception e){
            throw new RRException(RRExceptionEnum.SERVICE_UNAVAILABLE);
        }
    }


    @Deprecated
    public void verify(BusinessLicenseContext businessLicenseContext) throws ExecutionException, InterruptedException, JsonProcessingException {

        String config = aiSysConfigService.selectConfigByKey("sys.agent.VerifyBusinessLicense");
        VerifyBusinessLicenseDTO verifyBusinessLicenseDTO = JsonUtils.fromJson(config, VerifyBusinessLicenseDTO.class);

        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(businessLicenseContext.getKeyId())
                .accessKeySecret(businessLicenseContext.getSecretKey())
                .build());

        AsyncClient client = AsyncClient.builder()
                .region(verifyBusinessLicenseDTO.getRegion())
                .credentialsProvider(provider)
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                .setEndpointOverride(verifyBusinessLicenseDTO.getEndpointOverride())
                )
                .build();

        VerifyBusinessLicenseRequest verifyBusinessLicenseRequest = VerifyBusinessLicenseRequest.builder()
                .creditCode(businessLicenseContext.getCreditCode())
                .companyName(businessLicenseContext.getCompanyName())
                .legalPerson(businessLicenseContext.getLegalPerson())
                .build();
        CompletableFuture<VerifyBusinessLicenseResponse> response = client.verifyBusinessLicense(verifyBusinessLicenseRequest);


        VerifyBusinessLicenseResponse resp = response.get();
        client.close();

        if(200 != resp.getStatusCode()){
            throw new RRException(RRExceptionEnum.SERVICE_UNAVAILABLE);
        }
        String resultData = resp.getBody().getData();
        VerifyBusinessLicenseVO result = parseToObj(resultData);
        client.close();
        if(!result.getData()){
            throw new RRException(result.getCode(),result.getMessage());
        }
        businessLicenseContext.setIsCertified(WiserConstant.CERTIFIED);
    }

    private VerifyBusinessLicenseVO parseToObj(String resultData) throws JsonProcessingException {
        String unescaped = StringEscapeUtils.unescapeJson(resultData);
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(unescaped);
//        JsonNode dataNode = rootNode.get("data");
        return mapper.treeToValue(rootNode, VerifyBusinessLicenseVO.class);
    }




}
