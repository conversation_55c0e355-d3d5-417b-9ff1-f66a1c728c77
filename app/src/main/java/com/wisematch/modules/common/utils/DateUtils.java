package com.wisematch.modules.common.utils;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DateUtils {


    public static Date getDefaultExpireTime(Integer day) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, day);
        return calendar.getTime();
    }
    // 修正后的正则表达式：严格区分年份、年月、年月日格式
    private static final String DATE_REGEX =
            "^(\\d{4})" +                          // 年份（必选）
                    "(?:[-./年](0?[1-9]|1[0-2]))?" +       // 月份部分（可选：分隔符+月份）
                    "(?:月)?" +                            // 月份后的“月”字（可选）
                    "(?:[-./](0?[1-9]|[12]\\d|3[01]))?" +  // 日期部分（可选：分隔符+日期）
                    "(?:日)?$";                            // 日期后的“日”字（可选）

    private static final Pattern DATE_PATTERN = Pattern.compile(DATE_REGEX);

    /**
     * 验证日期格式是否匹配
     */
    public static boolean isValidDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) return false;
        return DATE_PATTERN.matcher(dateStr.trim()).matches();
    }

    /**
     * 提取日期中的年月日信息
     */
    public static Map<String, String> extractDateParts(String dateStr) {
        Map<String, String> result = new HashMap<>();
        if (dateStr == null) return result;

        Matcher matcher = DATE_PATTERN.matcher(dateStr.trim());
        if (matcher.matches()) {
            // 提取年份（第1组）
            result.put("year", matcher.group(1));

            // 提取月份（第2组，若存在）
            String month = matcher.group(2);
            if (month != null) {
                // 去除前导0（如"09"→"9"）
                month = month.startsWith("0") && month.length() > 1 ? month.substring(1) : month;
                result.put("month", month);
            }

            // 提取日期（第3组，若存在）
            String day = matcher.group(3);
            if (day != null) {
                // 去除前导0（如"05"→"5"）
                day = day.startsWith("0") && day.length() > 1 ? day.substring(1) : day;
                result.put("day", day);
            }
        }
        return result;
    }

    /**
     * 标准化日期格式（统一转换为YYYY-MM-DD）
     */
    public static String normalizeDate(String dateStr) {
        Map<String, String> parts = extractDateParts(dateStr);
        if (parts.isEmpty()) return dateStr;

        String year = parts.get("year");
        String month = parts.getOrDefault("month", "01"); // 无月份时默认1月
        String day = parts.getOrDefault("day", "01");     // 无日期时默认1日

        // 补全为两位（如"9"→"09"）
        month = month.length() == 1 ? "0" + month : month;
        day = day.length() == 1 ? "0" + day : day;

        return year + "-" + month + "-" + day;
    }

    public static void main(String[] args) {
        String[] testDates = {
                // 仅年份（有效）
                "2021", "1999",

                // 年月格式（重点测试）
                "2021年9月", "2021年09月", "2021-9", "2021-09",
                "2021.9", "2021.09", "2021/9", "2021/09",

                // 年月日格式
                "2021年09月15日", "2021年9月15日", "2021-09-15",
                "2021.9.15", "2021/09/15", "2021年9月1日",

                // 无效格式（应排除）
                "2021年12月",  "2021年01月", "2021年1月","2021-00", "2021.9.32", "abc2021", "2021-", "2021年"
        };

        System.out.println("日期格式验证结果:");
        System.out.println("=".repeat(70));
        System.out.printf("%-20s | %-6s | %-20s | %-12s%n",
                "原始日期", "有效", "解析结果", "标准化格式");
        System.out.println("=".repeat(70));

        for (String date : testDates) {
            boolean valid = isValidDate(date);
            Map<String, String> parts = extractDateParts(date);
            String normalized = valid ? normalizeDate(date) : "无效格式";

            System.out.printf("%-20s | %-6b | %-20s | %-12s%n",
                    date, valid, parts, normalized);
        }
    }
}
