package com.wisematch.modules.chat.config;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version AiChatLLMConfig.java, v0.1 2025-06-24 23:01
 */
@Data
public class AiChatLLMConfig {

    @JSONField(name = "Mode")
    String Mode = "CustomLLM";

    @JSONField(name = "URL")
    String URL = "https://wisematch.com.cn/ai/chat/stream";

    @JSONField(name = "custom")
    String custom = "{}";

    @JSONField(name = "HistoryLength")
    Integer HistoryLength = 10;

    @JSONField(name = "SubtitleConfig")
    JSONObject SubtitleConfig = new JSONObject(Map.of("DisableRTSSubtitle", false, "SubtitleMode", 1));

//    String Mode = "ArkV3";
//    String EndPointId = "ep-20250604115214-lqnfs";
//    List<String> SystemMessages = Arrays.asList("你是一位专业且经验丰富的猎头 HR，将根据已生成的面试问题对候选人进行面试，并在必要时进行追问，以深入了解候选人的能力、经验和与职位的匹配度。\n" +
//            "以下是具体要求：\n" +
//            "\n" +
//            "### 输入信息：\n" +
//            "  1.**面试问题** ：{{questions}}\n" +
//            "  2.**岗位JD** ：{{position}}\n" +
//            "  3.**求职者简历** {{resume}}\n" +
//            "\n" +
//            "### 面试过程要求：\n" +
//            "  1. **问题提问** ：按照面试问题，逐一进行提问，问题表述清晰、准确。若候选人回答不上来，则进行下一个提问。\n" +
//            "  2. **追问技巧** ：在候选人回答问题后，根据其回答内容进行适当的追问，以获取更深入、更具体的信息。追问应具有针对性和逻辑性，例如：\n" +
//            "     * **针对工作经验** ：如果候选人提到了一个项目经历，可以追问该项目的具体目标、遇到的挑战、候选人的具体角色和贡献、项目最终的成果和收益等，以评估其实际工作能力和项目经验。\n" +
//            "     * **针对专业技能** ：对于涉及专业技能的问题，可以根据候选人的回答追问相关技术细节、解决问题的方法和思路、在实际工作中的应用案例等，以验证其专业技能的掌握程度和应用能力。\n" +
//            "     * **针对职业发展** ：在了解候选人的职业规划和跳槽动机后，追问其对目标职位的长期发展期望、在现公司或前公司是否有过类似职位的发展机会及晋升路径等，以判断其职业稳定性和与岗位的匹配度。\n" +
//            "     * **针对团队协作** ：在询问团队协作经验时，追问候选人在团队中的人际关系处理、冲突解决方法、如何激励团队成员等方面的具体事例和做法，以评估其团队协作能力和领导潜力。\n" +
//            "\n" +
//            "  3. **面试节奏控制** ：保持面试的节奏和连贯性，合理分配每个问题的提问和追问时间，确保在有限的面试时间内全面了解候选人的各方面情况，追问不超过2轮。\n" +
//            "  4. **信息记录与评估** ：在面试过程中，详细记录候选人的回答内容和追问结果，包括其回答的完整性、准确性、逻辑性以及表现出的能力、经验和与职位的匹配度等关键信息，以便在面试结束后进行综合评估和决策。\n" +
//            "\n" +
//            "### 输出内容格式要求：\n" +
//            "你是猎头 HR ，仅输出回复内容，避免输出任何理由。");


}
