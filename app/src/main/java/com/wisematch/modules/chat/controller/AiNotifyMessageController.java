package com.wisematch.modules.chat.controller;

import com.wisematch.common.lock.NotDoubleSubmit;
import com.wisematch.common.utils.R;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.chat.service.AiNotifyMessageService;
import com.wisematch.modules.chat.service.AiViewPortraitService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/notifyMessage")
@Tag(name = "消息通知", description = "消息通知")
public class AiNotifyMessageController {

    @Autowired
    AiNotifyMessageService aiNotifyMessageService;
    @Autowired
    AiViewPortraitService aiViewPortraitService;


    @PostMapping("/detail")
    @Operation(summary = "id获取详情")
    public R detail(@RequestBody IdRequest idRequest) {
        return R.ok().setData(aiNotifyMessageService.getMsgById(idRequest));
    }

    @PostMapping("/addApplyMessage")
    @Operation(summary = "申请查看用户面试视屏")
    @Deprecated
    public R addApplyMessage(@RequestBody AddApplyMessageDTO dto){
        aiNotifyMessageService.addApplyMessage(dto);
        return R.ok();
    }

    @PostMapping("/getByUserId")
    @Operation(summary = "分页查询当前用户通知消息")
    public R pageQuery(@RequestBody AiNotifyMessagePageDTO dto){
        return R.ok().setData(aiNotifyMessageService.getByUserId(dto));
    }

    @PostMapping("/communicate")
    @Operation(summary = "在线沟通（微信，手机号，面试视频）")
    @NotDoubleSubmit
    public R communicate(@RequestBody NotifyCommunicatDTO dto){
        return R.ok().setData(aiNotifyMessageService.notifyCommunicat(dto));
    }

    @PostMapping("/communicateAction")
    @Operation(summary = "在线沟通同意拒绝（微信，手机号，面试视频）")
    public R communicateAction(@RequestBody NotifyCommunicatActionDTO dto){
        return R.ok().setData(aiNotifyMessageService.communicationAction(dto));
    }

    @PostMapping("/communicateList")
    @Operation(summary = "沟通记录")
    @NotDoubleSubmit
    public R communicateList(@RequestBody NotifyCommunicatListDTO dto){
        return R.ok().setData(aiNotifyMessageService.communicationList(dto));
    }

    @PostMapping("/communicateCard")
    @NotDoubleSubmit
    @Operation(summary = "在线沟通上方card")
    public R communicateCard(@RequestBody CommunicateCardDTO dto){
        return R.ok().setData(aiViewPortraitService.communicateCard(dto));
    }

}

