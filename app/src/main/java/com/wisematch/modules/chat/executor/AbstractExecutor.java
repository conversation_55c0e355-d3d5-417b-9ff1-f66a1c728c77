package com.wisematch.modules.chat.executor;

import com.wisematch.common.utils.ThreadPoolUtil;
import com.wisematch.modules.chat.handler.viewer.ViewHandlerContext;

/**
 * <AUTHOR>
 * @version AbstractExecutor.java, v0.1 2025-07-19 22:41
 */
public abstract class AbstractExecutor {

    public boolean execute(ViewHandlerContext context){
        return true;
    }

    public void executeAsync(ViewHandlerContext context){
        ThreadPoolUtil.supplyAsync(() -> this.execute(context));
    }
}
