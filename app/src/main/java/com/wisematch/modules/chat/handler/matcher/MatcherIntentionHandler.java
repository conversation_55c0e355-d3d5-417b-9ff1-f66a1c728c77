package com.wisematch.modules.chat.handler.matcher;

import com.wisematch.modules.chat.agent.MatcherIntentionAgent;
import com.wisematch.modules.chat.entity.AiAgentPool;
import com.wisematch.modules.chat.handler.AgentHandlerContext;
import com.wisematch.modules.chat.handler.IAgentHandler;
import com.wisematch.modules.chat.model.IntentionVO;
import com.wisematch.modules.chat.model.ChatMessage;
import com.wisematch.modules.chat.service.AiAgentPoolService;
import com.wisematch.modules.chat.service.AiChatMatcherMemoryService;
import com.wisematch.modules.chat.service.AiChatWiserMemoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.messages.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * Matcher意图识别
 * <AUTHOR>
 * @version ResumeCheckHandler.java, v0.1 2025-07-15 16:49
 */
@Component
@Slf4j
public class MatcherIntentionHandler implements IAgentHandler {

    IAgentHandler nextHandler;

    @Autowired
    private MatcherIntentionAgent matcherIntentionAgent;

    @Autowired
    private AiChatMatcherMemoryService aiChatMatcherMemoryService;

    @Autowired
    private AiAgentPoolService aiAgentPoolService;

    @Override
    public void setNext(IAgentHandler iAgentHandler) {
        this.nextHandler = iAgentHandler;
    }

    @Override
    public Flux<ChatMessage> handle(AgentHandlerContext context) {
        log.info("matcher intention start, chatId: {}", context.getAiChatUserMsg().getChatId());
        IntentionVO intention = matcherIntentionAgent.getIntention(context, context.getAiChatUserMsg().getMsg());
        context.setIntentionVO(intention);
        if (intention.getAgentId() != null) {
            context.setAiAgentPool(this.aiAgentPoolService.getById(context.getIntentionVO().getAgentId()));
        } else {
            context.setAiAgentPool(this.aiAgentPoolService.getByCode(context.getIntentionVO().getAgentCode()));
        }
        return nextHandler.handle(context);
    }
}
