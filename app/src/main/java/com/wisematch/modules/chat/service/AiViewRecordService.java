package com.wisematch.modules.chat.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.entity.AiViewRecord;
import com.wisematch.modules.chat.model.GenerateQuestionDTO;
import com.wisematch.modules.chat.model.InterviewInitParams;
import com.wisematch.modules.chat.model.PreApplyVO;

import java.util.List;


/**
 * AI面试服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:43:39
 */
public interface AiViewRecordService extends IService<AiViewRecord> {

    List<String> getByUserId(String userId);

    AiViewRecord getByRoomId(String roomId);

    List<AiViewRecord> getChattingRoom();

    List<AiViewRecord> getList();

    Integer getChattingCount(String userId);

    Boolean applySubmit(JSONObject applyInfos);

    AiViewRecord initViewRecord(InterviewInitParams initParams);

    List<AiViewRecord> getViewRecord(String userId, String type);

    List<String> generateQuestion(GenerateQuestionDTO generateQuestionDTO);

    PreApplyVO preApply(String userId);

}
