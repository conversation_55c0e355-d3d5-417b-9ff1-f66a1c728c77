package com.wisematch.modules.chat.service;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.util.Base64;
import java.util.Random;

@Component
public class AdvancedCaptchaGenerator {

    private static final String CHAR_SET = "23456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnopqrstuvwxyz";
    private static final int WIDTH = 150;
    private static final int HEIGHT = 50;
    private static final int CODE_LENGTH = 5;

    // 验证码结果封装类
    @Data
    @AllArgsConstructor
    public static class CaptchaResult {
        private String code;        // 验证码文本
        private String imageBase64; // Base64编码的图片
    }
    public CaptchaResult generateAdvancedCaptcha() {
        BufferedImage image = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();

        // 设置抗锯齿
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 渐变背景
        GradientPaint gradient = new GradientPaint(0, 0, Color.LIGHT_GRAY, WIDTH, HEIGHT, Color.WHITE);
        g.setPaint(gradient);
        g.fillRect(0, 0, WIDTH, HEIGHT);

        String captchaCode = generateRandomCode();

        // 绘制扭曲的文字
        drawDistortedText(g, captchaCode);

        // 添加噪点
        addNoise(image);

        g.dispose();

        String base64 = imageToBase64(image);
        return new CaptchaResult(captchaCode, base64);
    }
    private String generateRandomCode() {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < CODE_LENGTH; i++) {
            code.append(CHAR_SET.charAt(random.nextInt(CHAR_SET.length())));
        }
        return code.toString();
    }

    private void drawDistortedText(Graphics2D g, String code) {
        Random random = new Random();
        g.setFont(new Font("Arial", Font.BOLD, 30));

        for (int i = 0; i < code.length(); i++) {
            // 随机颜色
            g.setColor(new Color(
                    random.nextInt(100) + 50,
                    random.nextInt(100) + 50,
                    random.nextInt(100) + 50
            ));

            // 扭曲变换
            AffineTransform original = g.getTransform();
            AffineTransform distort = new AffineTransform();
            distort.rotate(Math.toRadians(random.nextInt(30) - 15),
                    20 + i * 25, 30);
            g.setTransform(distort);

            g.drawString(String.valueOf(code.charAt(i)), 20 + i * 25, 30 + random.nextInt(10) - 5);
            g.setTransform(original);
        }
    }

    private void addNoise(BufferedImage image) {
        Random random = new Random();
        for (int i = 0; i < 100; i++) {
            int x = random.nextInt(WIDTH);
            int y = random.nextInt(HEIGHT);
            image.setRGB(x, y, Color.GRAY.getRGB());
        }
    }

    private String imageToBase64(BufferedImage image) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            ImageIO.write(image, "png", baos);
            byte[] imageBytes = baos.toByteArray();
            return "data:image/png;base64," + Base64.getEncoder().encodeToString(imageBytes);
        } catch (Exception e) {
            throw new RuntimeException("生成Base64图片失败", e);
        }
    }
}
