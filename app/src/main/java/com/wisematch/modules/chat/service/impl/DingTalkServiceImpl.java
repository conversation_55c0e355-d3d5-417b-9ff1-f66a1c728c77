package com.wisematch.modules.chat.service.impl;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.wisematch.modules.chat.agent.AgentContext;
import com.wisematch.modules.chat.agent.AgentFacade;
import com.wisematch.modules.chat.enums.AlertAgentConst;
import com.wisematch.modules.chat.service.DingTalkService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@Service
public class DingTalkServiceImpl implements DingTalkService {

    @Value("${ding-talk.url}")
    private String baseUrl;

    @Value("${ding-talk.access-token}")
    private String accessToken;

    @Value("${ding-talk.secret}")
    private String secret;

    @Resource
    private DingTalkClient dingTalkClient;

    @Resource
    private AgentFacade agentFacade;

    @Resource
    private ThreadPoolExecutor dingTalkHttpExecutor;


    @Override
    public void sendMsgToAlertWebHook(String errorMsg) {
        if (!StringUtils.hasLength(secret)) {
            return;
        }

        dingTalkHttpExecutor.execute(() -> {
            String prompt = agentFacade.getPrompt(AlertAgentConst.LOG_ALERT_AGENT);
            prompt = prompt.replace("$errorMsg", errorMsg);
            AgentContext agentContext = new AgentContext();
            agentContext.setAgentCode(AlertAgentConst.LOG_ALERT_AGENT);
            agentContext.setPrompt(prompt);
            agentContext.setUserMsg("输出日志报错报告");
            String reply =  agentFacade.supply(agentContext);

            String finalReply = ("## 原始错误 \n" + errorMsg + "\n\n" + reply).replaceAll("```markdown", "").replaceAll("```", "");

            try {
                Long timestamp = System.currentTimeMillis();
                String sign = calculateSign(timestamp, secret);
                String fullUrl = String.format("%s?timestamp=%d&sign=%s", baseUrl, timestamp, sign);
                ((DefaultDingTalkClient) dingTalkClient).resetServerUrl(fullUrl);
                OapiRobotSendRequest req = new OapiRobotSendRequest();
                req.setMsgtype("markdown");
                OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
                markdown.setTitle("日志报警");
                markdown.setText(finalReply);
                req.setMarkdown(markdown);
                dingTalkClient.execute(req, accessToken);
            } catch (Exception e) {
                log.error("发送钉钉消息失败", e);
            }
        });
    }

    // 签名计算方法
    private String calculateSign(Long timestamp, String secret) throws Exception {
        String stringToSign = timestamp + "\n" + secret;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
        return URLEncoder.encode(new String(Base64.encodeBase64(signData)), StandardCharsets.UTF_8);
    }
}
