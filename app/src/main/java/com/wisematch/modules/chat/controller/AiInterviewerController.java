package com.wisematch.modules.chat.controller;

import com.alibaba.fastjson.JSONObject;
import com.wisematch.common.annotation.SysLog;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.common.utils.HttpContextUtils;
import com.wisematch.common.utils.InterviewThreadPoolUtil;
import com.wisematch.common.utils.R;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.agent.RoomAgentFacade;
import com.wisematch.modules.chat.enums.ApplyType;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.chat.service.AiChatLogService;
import com.wisematch.modules.chat.service.AiViewRecordService;
import com.wisematch.modules.chat.service.CardInfoService;
import com.wisematch.modules.chat.service.impl.AiChatViewFacade;
import com.wisematch.modules.chat.service.impl.AiViewCardInfoServiceImpl;
import com.wisematch.modules.sys.annotation.Anonymous;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @version AiChatController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/chat")
@Tag(name = "AI面试", description = "AI面试")
@Slf4j
public class AiInterviewerController {

    @Autowired
    private AiViewRecordService aiChatService;

    @Autowired
    private AiViewCardInfoServiceImpl aiViewCardInfoService;

    @Autowired
    AiChatLogService aiChatLogService;

    @Autowired
    CardInfoService cardInfoService;

    @Autowired
    RoomAgentFacade roomAgentFacade;

    @Autowired
    AiChatViewFacade aiChatViewFacade;

    @Operation(summary = "发送消息")
    @PostMapping("/send")
    public R send(@RequestBody AiChatUserMsg userMsg) {
        return R.ok().setData(aiChatViewFacade.sendMsg(userMsg));
    }

    @Operation(summary = "流式消息")
    @Anonymous
    @PostMapping(path = "/stream", consumes = MediaType.ALL_VALUE, produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @SysLog("流式对话面试")
    public Flux<Object> chat(@RequestBody Object param) {
        return aiChatViewFacade.stream(param);
    }


    @Operation(summary = "检测面试房间状态(true/有效，false:关闭)")
    @GetMapping("/room/check")
    @SysLog("检测面试房间状态")
    public R checkRoomStatus(@RequestParam("roomId") String roomId) {
        return R.ok().setData(aiChatViewFacade.checkRoomStatus(roomId));
    }

    @Operation(summary = "申请岗位前获取岗位申请记录中的部分信息", parameters = {
            @Parameter(
                    name = "ApplyViewParams",
                    description = "申请岗位前获取岗位申请记录中的部分信息")
    })
    @GetMapping("/preApply")
    public R preApply() {
        return R.ok().setData(aiChatService.preApply(UserInfoUtils.getCurrentUserId()));
    }

    @Operation(summary = "离开提示信息", parameters = {
            @Parameter(
                    name = "roomId",
                    description = "面试房间roomId")
    })
    @SysLog("离开提示信息")
    @GetMapping("/apply/stop/tips")
    public R getStopTips(@RequestParam("roomId") String roomId) {
        return R.ok().setData(aiChatViewFacade.applyStop(roomId));
    }

    @Operation(summary = "提交表单投递岗位", parameters = {
            @Parameter(
                    name = "applyInfos",
                    description = "提交表单投递岗位")
    })
    @SysLog("提交表单投递岗位")
    @PostMapping("/apply/submit")
    public R applySubmit(@RequestBody JSONObject applyInfos) {
        return R.ok().setData(aiChatService.applySubmit(applyInfos));
    }


    @Operation(summary = "申请岗位面试", parameters = {
            @Parameter(
                    name = "ApplyViewParams",
                    description = "申请岗位面试参数")
    })
    @PostMapping("/apply/position")
    @SysLog("申请岗位面试")
    public R applyPosition(@RequestBody ApplyViewParams applyViewParams) {
        if (!aiChatViewFacade.checkRoomLimit()) {
            return R.error(RRExceptionEnum.VIEW_ROOM_LIMIT);
        }
        applyViewParams.setApplyType(ApplyType.POSITION.name());
        return R.ok().setData(aiChatViewFacade.applyStart(applyViewParams));
    }

    @Operation(summary = "申请模拟面试", parameters = {
            @Parameter(
                    name = "ApplyViewParams",
                    description = "申请模拟面试参数")
    })
    @SysLog("申请模拟面试")
    @PostMapping("/apply/train")
    public R applyTrain(@RequestBody ApplyViewParams applyViewParams) {
        if (!aiChatViewFacade.checkRoomLimit()) {
            return R.error(RRExceptionEnum.VIEW_ROOM_LIMIT);
        }
        applyViewParams.setApplyType(ApplyType.TRAIN.name());
        return R.ok().setData(aiChatViewFacade.applyStart(applyViewParams));
    }

    @Operation(summary = "启动面试智能体")
    @PostMapping("/start/agent")
    @SysLog("启动面试智能体")
    public R startAgent(@RequestBody StartAgentParam agentParam) {
        agentParam.setUserId(UserInfoUtils.getCurrentUserId());
        agentParam.setUserAgent(HttpContextUtils.getHttpServletRequest().getHeader("User-Agent"));
        InterviewThreadPoolUtil.supplyAsync(() -> roomAgentFacade.startRoomAgent(agentParam)).thenAccept(result -> {
            log.info("启动成功: {}", result);
        });
        return R.ok().setData(true);
    }

    @Operation(summary = "打断智能体")
    @PostMapping("/interrupt/agent")
    @SysLog("打断智能体")
    public R interruptAgent(@RequestBody UpdateAgentParam agentParam) {
        agentParam.setCommand("interrupt");
        InterviewThreadPoolUtil.supplyAsync(() -> roomAgentFacade.updateAgent(agentParam)).thenAccept(result -> {
            log.info("执行成功: {}", result);
        });
        return R.ok().setData(true);
    }

    @Operation(summary = "超时通知智能体")
    @PostMapping("/notice/agent")
    @SysLog("超时通知智能体")
    public R noticeAgent(@RequestBody UpdateAgentParam agentParam) {
        agentParam.setCommand("ExternalTextToSpeech");
        agentParam.setMessage(roomAgentFacade.getRoomNotice(agentParam.getRoomId()));
        InterviewThreadPoolUtil.supplyAsync(() -> roomAgentFacade.updateAgent(agentParam)).thenAccept(result -> {
            log.info("执行成功: {}", result);
        });
        return R.ok().setData(true);
    }

    @Operation(summary = "结束面试智能体")
    @PostMapping("/stop/agent")
    @SysLog("结束面试智能体")
    public R stopAgent(@RequestBody StopAgentParam agentParam) {
        InterviewThreadPoolUtil.supplyAsync(() -> roomAgentFacade.stopRoomAgent(agentParam)).thenAccept(result -> {
            log.info("执行成功: {}", result);
        });
        return R.ok().setData(true);
    }

    @Operation(summary = "问题清单生成")
    @PostMapping("/generateQuestion")
    public R generateQuestion(@RequestBody GenerateQuestionDTO generateQuestionDTO) {
        return R.ok().setData(aiChatService.generateQuestion(generateQuestionDTO));
    }

    @Operation(summary = "获取岗位投递记录(废弃)")
    @GetMapping("/apply/position/record")
    @Deprecated
    public R getPositionRecord() {
        return R.ok().setData(aiViewCardInfoService.getViewRecord(UserInfoUtils.getCurrentUserId(), ApplyType.POSITION.name()));
    }

    @Operation(summary = "获取模拟面试记录")
    @GetMapping("/apply/train/record")
    public R getTrainRecord() {
        return R.ok().setData(aiViewCardInfoService.getViewRecord(UserInfoUtils.getCurrentUserId(), ApplyType.TRAIN.name()));
    }

    @Operation(summary = "获取岗位投递记录")
    @GetMapping("/my/position/record")
    public R getMyPositionRecord() {
        return R.ok().setData(cardInfoService.getApplyRecord(UserInfoUtils.getCurrentUserId()));
    }

}
