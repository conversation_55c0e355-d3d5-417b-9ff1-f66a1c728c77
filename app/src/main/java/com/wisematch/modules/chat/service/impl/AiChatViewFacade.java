package com.wisematch.modules.chat.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.agent.RoomAgentFacade;
import com.wisematch.modules.chat.config.AiChatConfig;
import com.wisematch.modules.chat.entity.AiJobPosition;
import com.wisematch.modules.chat.entity.AiJobTrain;
import com.wisematch.modules.chat.entity.AiJobViewer;
import com.wisematch.modules.chat.entity.AiViewRecord;
import com.wisematch.modules.chat.enums.*;
import com.wisematch.modules.chat.executor.ViewInitQuestionExecutor;
import com.wisematch.modules.chat.handler.AiInterviewAgentFacade;
import com.wisematch.modules.chat.handler.viewer.ViewHandlerContext;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.chat.service.*;
import com.wisematch.modules.common.service.AiSysConfigService;
import com.wisematch.modules.rtc.AccessToken;
import com.wisematch.modules.rtc.Utils;
import com.wisematch.modules.sys.entity.SysUser;
import com.wisematch.modules.sys.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.wisematch.modules.chat.enums.WiserConstant.CHAT_ROOM_ID_PRE;

/**
 * AI面试门面
 * <AUTHOR>
 * @version AiInterviewFacade.java, v0.1 2025-07-18 15:13
 */
@Component
@Slf4j
public class AiChatViewFacade {
    @Autowired
    private AiViewRecordService aiViewRecordService;
    @Autowired
    private AiInterviewAgentFacade aiInterviewAgentFacade;
    @Autowired
    private AiChatConfig aiChatConfig;
    @Autowired
    private AiJobTrainService aiJobTrainService;
    @Autowired
    private AiJobPositionService aiJobPositionService;
    @Autowired
    private ViewInitQuestionExecutor viewInitQuestionExecutor;
    @Autowired
    private RoomAgentFacade roomAgentFacade;
    @Autowired
    private AiJobViewerService aiJobViewerService;
    @Autowired
    private AiSysConfigService aiSysConfigService;
    @Autowired
    private AiUserResumeService aiUserResumeService;
    @Autowired
    private SysUserService sysUserService;

    /**
     * 面试人数限制判断
     * @return
     */
    public boolean checkRoomLimit() {
        JSONObject roomConfig = aiSysConfigService.getInterviewConfig();
        Integer roomLimit = roomConfig.getInteger("room_limit");
        String roomWhiteList = roomConfig.getString("room_white_list");
        if (roomWhiteList.contains(UserInfoUtils.getUsername())) {
            return true;
        }
        Integer chattingCount = aiViewRecordService.getChattingCount(UserInfoUtils.getCurrentUserId());
        log.info("当前正在面试人数：{} 人", chattingCount);
        if (chattingCount >= roomLimit) {
            return false;
        }
        return true;
    }

    /**
     * 申请开始面试
     * @param applyViewParams
     * @return
     */
    public AiChatSetting applyStart(ApplyViewParams applyViewParams) {

        InterviewInitParams initParams = new InterviewInitParams();
        initParams.setUserId(UserInfoUtils.getCurrentUserId());
        initParams.setApplyInfos(applyViewParams.getApplyInfos());
        initParams.setApplyType(applyViewParams.getApplyType());
        initParams.setPlatform(applyViewParams.getPlatform());

        SysUser sysUser = sysUserService.getById(UserInfoUtils.getCurrentUserId());
        if(!WiserConstant.CERTIFICATED_SUCCESS.equals(sysUser.getHasCertification())){
            throw new RRException(RRExceptionEnum.NOT_CERTIFICATED);
        }

        if (ApplyType.POSITION.name().equals(applyViewParams.getApplyType())) {
            aiUserResumeService.examResume(UserInfoUtils.getCurrentUserId());
//            UserInfoUtils.examCertificated();
            AiJobPosition aiJobPosition = this.aiJobPositionService.getById(applyViewParams.getApplyId());
//            initParams.setAgentId(aiJobPosition.getAgentId());
            initParams.setViewId(aiJobPosition.getJobViewerId());
            initParams.setPositionId(applyViewParams.getApplyId());
            initParams.setApplyPosition(aiJobPosition.getPosition());
            initParams.setApplyName(applyViewParams.getApplyName());
            initParams.setApplyPhone(applyViewParams.getApplyPhone());
            initParams.setApplyEmail(applyViewParams.getApplyEmail());
            initParams.setWelcome(aiJobPosition.getWelcome());
        } else {
            AiJobTrain aiJobTrain = this.aiJobTrainService.getById(applyViewParams.getApplyId());
            if(PositionTrainType.MOCK_INTERVIEW.getCode().equals(aiJobTrain.getType())){
                aiUserResumeService.examResume(UserInfoUtils.getCurrentUserId());
            }
            initParams.setAgentId(aiJobTrain.getAgentId());
            initParams.setViewId(aiJobTrain.getJobViewerId());
            initParams.setTrainId(applyViewParams.getApplyId());
            initParams.setApplyPosition(aiJobTrain.getPosition());
            initParams.setWelcome(aiJobTrain.getWelcome());
        }

        AiChatSetting aiChatSetting = initToken(initParams);
        JSONObject settings = aiSysConfigService.getInterviewConfig();
        if (settings.getInteger("width") != null) {
            if (PlatformType.PC.name().equals(applyViewParams.getPlatform())) {
                aiChatSetting.setWidth(settings.getInteger("width"));
            } else {
                aiChatSetting.setHeight(settings.getInteger("width"));
            }
        }
        if (settings.getInteger("height") != null) {
            if (PlatformType.PC.name().equals(applyViewParams.getPlatform())) {
                aiChatSetting.setHeight(settings.getInteger("height"));
            } else {
                aiChatSetting.setWidth(settings.getInteger("height"));
            }
        }
        return aiChatSetting;
    }

    public ApplyStopVO applyStop(String chatRoomId) {
        AiViewRecord aiViewRecord = aiViewRecordService.getByRoomId(chatRoomId);
        return applyStop(aiViewRecord);
    }

        /**
         * 中途暂停面试
         * @return
         */
    public ApplyStopVO applyStop(AiViewRecord aiViewRecord) {
        ApplyStopVO applyStopVO = new ApplyStopVO();
        //判定面试时间是否足够
        JSONObject params = aiSysConfigService.getInterviewConfig();
        long miniDuration = params.getInteger(InterviewConstant.MINI_DURATION);
        if (aiViewRecord.getStartTime() != null) {
            Date current = new Date();
            long time = (current.getTime() - aiViewRecord.getStartTime().getTime()) / 1000;
            if (time < miniDuration) {
                applyStopVO.setTips("你的面试时间不足" + (miniDuration / 60) + "分钟，放弃后本次面试将无效");
                applyStopVO.setAllowSubmit(false);
            }else {
                applyStopVO.setAllowSubmit(true);
            }
        }
        if (ApplyType.POSITION.name().equals(aiViewRecord.getChatType())) {
            AiJobPosition aiJobPosition = aiJobPositionService.getById(aiViewRecord.getPositionId());
            aiViewRecord.setApplyType(ApplyPositionType.FULL.name());
            applyStopVO.setFormType(ApplyPositionType.FULL.name());
            if (aiJobPosition.getType().contains("兼职")) {
                applyStopVO.setFormType(ApplyPositionType.PART.name());
                aiViewRecord.setApplyType(ApplyPositionType.PART.name());
            }
        }
        return applyStopVO;
    }

    public AiChatSetting initToken(InterviewInitParams initParams){
        log.info("init token begin:{}", JSONObject.toJSONString(initParams));
        long start = System.currentTimeMillis();
        //幂等控制
        AiViewRecord aiViewRecord = aiViewRecordService.initViewRecord(initParams);
//        if (aiViewRecord != null && StringUtils.isNotEmpty(aiViewRecord.getRoomToken())
//                && InterviewStatus.INIT.name().equals(aiViewRecord.getStatus())) {
//            AiChatSetting aiChatSetting = new AiChatSetting(aiViewRecord);
//            AiJobViewer aiJobViewer = aiJobViewerService.getById(aiViewRecord.getViewerId());
//            if (aiJobViewer != null) {
//                aiChatSetting.setSpeakPhoto(StringUtils.isNotBlank(aiJobViewer.getSpeakUrl()) ? aiJobViewer.getSpeakUrl() : aiJobViewer.getPhoto());
//                aiChatSetting.setListenPhoto(StringUtils.isNotBlank(aiJobViewer.getListenUrl()) ? aiJobViewer.getListenUrl() : aiJobViewer.getPhoto());
//                aiChatSetting.setViewerPhoto(aiChatSetting.getListenPhoto());
//            }
//            aiChatSetting.setAppId(aiChatConfig.getAppId());
//            return aiChatSetting;
//        }
//        aiViewRecord = aiViewRecordService.initViewRecord(initParams);
        String chatRoomId = CHAT_ROOM_ID_PRE + IdUtil.fastSimpleUUID().substring(0, 20);
        AccessToken token = new AccessToken(aiChatConfig.getAppId(), aiChatConfig.getAppKey(), chatRoomId, initParams.getUserId());
        token.ExpireTime(Utils.getTimestamp() + 14400);
        token.AddPrivilege(AccessToken.Privileges.PrivSubscribeStream, 0);
        token.AddPrivilege(AccessToken.Privileges.PrivPublishStream, Utils.getTimestamp() + 14400);
        aiViewRecord.setRoomId(chatRoomId);
        aiViewRecord.setRoomToken(token.Serialize());
        aiViewRecord.setUpdateTime(new Date());
        aiViewRecordService.save(aiViewRecord);

        //根据用户简历信息异步初始化用户问题清单
        viewInitQuestionExecutor.asyncExecute(aiViewRecord);

        AiChatSetting aiChatSetting = new AiChatSetting(aiViewRecord);
        aiChatSetting.setAppId(aiChatConfig.getAppId());
        aiChatSetting.setPosition(initParams.getApplyPosition());

        AiJobViewer aiJobViewer = aiJobViewerService.getById(aiViewRecord.getViewerId());
        if (aiJobViewer != null) {
            aiChatSetting.setSpeakPhoto(StringUtils.isNotBlank(aiJobViewer.getSpeakUrl()) ? aiJobViewer.getSpeakUrl() : aiJobViewer.getPhoto());
            aiChatSetting.setListenPhoto(StringUtils.isNotBlank(aiJobViewer.getListenUrl()) ? aiJobViewer.getListenUrl() : aiJobViewer.getPhoto());
            aiChatSetting.setViewerPhoto(aiChatSetting.getListenPhoto());
        }
        log.info("init token cost: {} ms", (System.currentTimeMillis()-start));
        return aiChatSetting;
    }

    public Flux<Object> stream(Object param) {
        long start = System.currentTimeMillis();
//        log.info("AI面试聊天入参:{}", JSONObject.toJSONString(param));
        JSONObject chatMsg = JSONObject.parseObject(JSONObject.toJSONString(param));
        ChatViewMsg msg = new ChatViewMsg();
        List<Object> messages = chatMsg.getJSONArray("messages");
        msg.setMessages(messages.stream().map(x -> {
            JSONObject y = (JSONObject) x;
            if (y.getString("role").equals(MessageType.ASSISTANT.getValue())) {
                return new AssistantMessage(y.getString("content"));
            } else {
                return new UserMessage(y.getString("content"));
            }
//            ShortMessage shortMessage = new ShortMessage();
//            shortMessage.setContent(y.getString("content"));
//            shortMessage.setRole(y.getString("role"));
//            return shortMessage;
        }).collect(Collectors.toList()));

        if (StringUtils.isNotEmpty(chatMsg.getString("custom"))) {
            msg.setCustom(chatMsg.getString("custom"));
        }

        if (StringUtils.isEmpty(msg.getCustom())) {
            ChatReplyMsg replyMsg = new ChatReplyMsg();
            replyMsg.setId(IdUtil.fastSimpleUUID());
            ChatReplyMsg.Choices choices = new ChatReplyMsg.Choices();
            choices.getDelta().put("content", "这是一条测试");
            replyMsg.setChoices(List.of(choices));
            return Flux.concat(Flux.just(replyMsg), Flux.just("[DONE]"));
        }
        ViewHandlerContext context = new ViewHandlerContext();
        context.setReplyId(IdUtil.fastSimpleUUID());
        context.setRoomId(JSONObject.parseObject(msg.getCustom()).getString("roomId"));
        context.setAiViewRecord(aiViewRecordService.getByRoomId(context.getRoomId()));
        context.setChatViewMsg(msg);
        roomAgentFacade.clearRoomNotice(context.getRoomId());
        return streamChat(context).doFinally(x -> {
            context.getAiViewRecord().setUpdateTime(new Date());
            aiViewRecordService.updateById(context.getAiViewRecord());
            log.info("面试回复耗时:{} ms", (System.currentTimeMillis() - start));
        });
    }

    public Flux<Object> streamChat(ViewHandlerContext context) {
        if (StringUtils.isEmpty(context.getChatViewMsg().getCustom())) {
            return Flux.just("[DONE]");
        }
        Flux<ChatReplyMsg> replys = aiInterviewAgentFacade.stream(context);
        ChatReplyMsg replyMsg = new ChatReplyMsg();
        replyMsg.setId(context.getReplyId());
        ChatReplyMsg.Choices choices = new ChatReplyMsg.Choices();
        choices.getDelta().put("content", "");
        choices.setFinish_reason("stop");
        replyMsg.setChoices(List.of(choices));
        return Flux.concat(replys, Flux.just(replyMsg), Flux.just("[DONE]"));
    }

    public ShortMessage sendMsg(AiChatUserMsg aiChatUserMsg){
        ViewHandlerContext context = new ViewHandlerContext();
        context.setReplyId(IdUtil.fastSimpleUUID());
        context.setRoomId(aiChatUserMsg.getChatId());
        context.setUserId(UserInfoUtils.getCurrentUserId());
        context.setAiViewRecord(aiViewRecordService.getByRoomId(context.getRoomId()));

        ChatViewMsg chatViewMsg = new ChatViewMsg();
        chatViewMsg.setMessages(List.of(new UserMessage(aiChatUserMsg.getMsg())));
        context.setChatViewMsg(chatViewMsg);
        Flux<ChatReplyMsg> replys = aiInterviewAgentFacade.stream(context).doFinally(x -> aiViewRecordService.updateById(context.getAiViewRecord()));
        ChatReplyMsg replyMsg = replys.blockFirst();
        ShortMessage message = new ShortMessage();
        message.setRole(MsgRole.assistant.name());
        if (replyMsg != null && replyMsg.getChoices() != null && !replyMsg.getChoices().isEmpty()) {
            message.setContent(replyMsg.getChoices().get(0).getDelta().getString("content"));
        }
        return message;
    }

    /**
     * 检测面试房间状态:true有效，false关闭
     * @param roomId
     * @return
     */
    public JSONObject checkRoomStatus(String roomId){
        JSONObject jsonObject = new JSONObject();
        AiViewRecord aiViewRecord = aiViewRecordService.getByRoomId(roomId);
        jsonObject.put(InterviewConstant.ROOM_OPEN, (aiViewRecord.getRoomStatus() == null || InterviewConstant.ROOM_OPEN.equals(aiViewRecord.getRoomStatus())));
        return jsonObject;
    }
}
