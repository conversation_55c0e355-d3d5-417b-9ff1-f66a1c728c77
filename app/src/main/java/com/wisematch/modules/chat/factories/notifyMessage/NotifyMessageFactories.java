package com.wisematch.modules.chat.factories.notifyMessage;

import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.entity.AiNotifyMessage;
import com.wisematch.modules.chat.enums.AiNotifyMessageType;
import com.wisematch.modules.chat.enums.MsgType;
import com.wisematch.modules.chat.enums.NotifyMsgBizSence;
import com.wisematch.modules.chat.model.NotifyCommunicatDTO;
import com.wisematch.modules.common.utils.DateUtils;
import org.springframework.beans.BeanUtils;

public class NotifyMessageFactories {


    public static AiNotifyMessage reportGenerate(String userId, String sourceId){
        AiNotifyMessage aiNotifyMessage = new AiNotifyMessage();
        aiNotifyMessage.setExpireTime(DateUtils.getDefaultExpireTime(30));
        aiNotifyMessage.setTitle("生成报告");
        aiNotifyMessage.setContent("报告已经生成，请注意查看");
        aiNotifyMessage.setBizSence(AiNotifyMessageType.REPORT.name());
        aiNotifyMessage.setReceiverId(userId);
        aiNotifyMessage.setSourceId(sourceId);
        aiNotifyMessage.setMsgType(MsgType.SHOULD_NOT_RECEIPT.name());
        aiNotifyMessage.setLogo("");
        return aiNotifyMessage;
    }


    public static AiNotifyMessage joinTalentPool(String userId, String sourceId){
        AiNotifyMessage aiNotifyMessage = new AiNotifyMessage();
        aiNotifyMessage.setExpireTime(DateUtils.getDefaultExpireTime(30));
        aiNotifyMessage.setTitle("加入人才池");
        aiNotifyMessage.setContent("恭喜你，加入岗位人才库，将会为您滚动推荐优质岗位。");
        aiNotifyMessage.setBizSence(AiNotifyMessageType.PORTRAIT.name());
        aiNotifyMessage.setReceiverId(userId);
        aiNotifyMessage.setSourceId(sourceId);
        aiNotifyMessage.setMsgType(MsgType.SHOULD_NOT_RECEIPT.name());
        aiNotifyMessage.setLogo("");
        return aiNotifyMessage;
    }


    public static AiNotifyMessage joinCandidateMsg(JoinCadidateDTO joinCadidateDTO){
        AiNotifyMessage aiNotifyMessage = new AiNotifyMessage();
        aiNotifyMessage.setExpireTime(DateUtils.getDefaultExpireTime(30));
        aiNotifyMessage.setTitle("加入候选人");
        aiNotifyMessage.setContent("投递的岗位被"+joinCadidateDTO.getCompanyName()+"加入候选人");
        aiNotifyMessage.setBizSence(AiNotifyMessageType.TALENT.name());
        aiNotifyMessage.setReceiverId(joinCadidateDTO.getUserId());
        aiNotifyMessage.setSenderId(joinCadidateDTO.getCompanyId());
        aiNotifyMessage.setMsgType(MsgType.SHOULD_RECEIPT.name());
        aiNotifyMessage.setSourceId(joinCadidateDTO.getSourceId());
        aiNotifyMessage.setLogo("");
        return aiNotifyMessage;
    }

    public static AiNotifyMessage systemMessages(SystemMessages systemMessages){
        AiNotifyMessage aiNotifyMessage = new AiNotifyMessage();
        aiNotifyMessage.setExpireTime(DateUtils.getDefaultExpireTime(30));
        aiNotifyMessage.setTitle(systemMessages.getTitle());
        aiNotifyMessage.setContent(systemMessages.getContent());
        aiNotifyMessage.setBizSence(AiNotifyMessageType.SYSTEM.name());
        aiNotifyMessage.setMsgType(MsgType.SHOULD_NOT_RECEIPT.name());
        aiNotifyMessage.setReceiverId(systemMessages.getUserId());
        aiNotifyMessage.setSourceId(systemMessages.getSourceId());
        aiNotifyMessage.setLogo("");
        return aiNotifyMessage;
    }

    public static AiNotifyMessage applyMessages(ApplyMessagesDTO applyMessages){
        AiNotifyMessage aiNotifyMessage = new AiNotifyMessage();
        BeanUtils.copyProperties(applyMessages,aiNotifyMessage);
        aiNotifyMessage.setBizSence(NotifyMsgBizSence.APPLY_VIDEO.name());
        aiNotifyMessage.setMsgType(MsgType.SHOULD_RECEIPT.name());
        aiNotifyMessage.setExpireTime(DateUtils.getDefaultExpireTime(30));
        return aiNotifyMessage;
    }

    public static AiNotifyMessage applyMessages(NotifyCommunicatDTO dto){
        AiNotifyMessage aiNotifyMessage = new AiNotifyMessage();
        BeanUtils.copyProperties(dto,aiNotifyMessage);
        aiNotifyMessage.setMsgType(MsgType.SHOULD_RECEIPT.name());
        aiNotifyMessage.setExpireTime(DateUtils.getDefaultExpireTime(30));
        aiNotifyMessage.setSenderPhoto(UserInfoUtils.getLoginUser().getUserPhoto());
        aiNotifyMessage.setSenderId(UserInfoUtils.getCurrentUserId());
        aiNotifyMessage.setReceiverId(dto.getReceiverId());
        aiNotifyMessage.setReceiverPhoto(dto.getReceiverPhoto());

        return aiNotifyMessage;
    }



}
