package com.wisematch.modules.chat.controller;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.wisematch.common.lock.NotDoubleSubmit;
import com.wisematch.common.utils.R;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.entity.AiUserResume;
import com.wisematch.modules.chat.model.AiUserResumeDTO;
import com.wisematch.modules.chat.model.ChangeResumeStatusDTO;
import com.wisematch.modules.chat.model.UserIdDTO;
import com.wisematch.modules.chat.model.UserResumeOnline;
import com.wisematch.modules.chat.service.AiUserResumeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;

import static com.wisematch.modules.chat.enums.WiserConstant.DELETED;

@RestController
@RequestMapping("/userResume")
@Tag(name = "用户简历", description = "用户简历管理")
public class AiUserResumeController {

    @Autowired
    private  AiUserResumeService aiUserResumeService;

    @PostMapping("/pageQuery")
    @Operation(summary = "搜索")
    @Deprecated
    public R pageQuery(
            @RequestBody AiUserResumeDTO aiUserResume) {

        return R.ok().setData(aiUserResumeService.pageQuery(aiUserResume));
    }

    @PostMapping("/getCurrentOpenedObj")
    @Operation(summary = "在线简历列表")
    public R getCurrentOpenedObj() {
        return R.ok().setData(aiUserResumeService.getCurrentOpenedObj(aiUserResumeService.getCurrentOpenedObj(UserInfoUtils.getCurrentUserId())));
    }


    @PostMapping("/online/list")
    @Operation(summary = "在线简历列表")
    public R online() {
        return R.ok().setData(aiUserResumeService.online(UserInfoUtils.getCurrentUserId()));
    }

    @GetMapping("/online/detail/{id}")
    @Operation(summary = "在线简历详情")
    public R getOnlineById(@PathVariable String id) {
        return R.ok().setData(aiUserResumeService.onlineDetail(id));
    }

    @PostMapping("/online/save")
    @Operation(summary = "保存在线简历")
    public R onlineSave(@RequestBody UserResumeOnline userResumeOnline) {
        return R.ok().setData(aiUserResumeService.onlineSave(userResumeOnline));
    }

    @PostMapping("/attachment/list")
    @Operation(summary = "附件简历列表")
    public R attachment() {
        return R.ok().setData(aiUserResumeService.attachmentList(UserInfoUtils.getCurrentUserId()));
    }

    @PostMapping("/changeStatus")
    @Operation(summary = "修改简历状态")
    @NotDoubleSubmit
    public R changeStatus(@RequestBody ChangeResumeStatusDTO dto) {
        aiUserResumeService.changeStatus(dto);
        return R.ok();
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除用户简历")
    public R delete(@PathVariable String id) {
        aiUserResumeService.update(new UpdateWrapper<AiUserResume>().lambda().eq(AiUserResume::getId,id)
                .set(AiUserResume::getIsDel,DELETED).set(AiUserResume::getUpdateTime,new Date()));
        return R.ok();
    }


    @PostMapping("/deleteByUserId")
    @Operation(summary = "根据用户删除简历")
    @NotDoubleSubmit
    public R deleteByUserId(@RequestBody UserIdDTO dto) {
        aiUserResumeService.deleteByUserId(dto.getUserId());
        return R.ok();
    }

    /**
     * 上传文件
     */
    @Operation(summary = "同步上传简历")
    @NotDoubleSubmit
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R upload(@RequestParam("file") MultipartFile file){
        return R.ok().setData(aiUserResumeService.uploadResume(file, UserInfoUtils.getCurrentUserId()));
    }

    /**
     * 上传文件
     */
    @Operation(summary = "异步上传简历")
    @NotDoubleSubmit
    @PostMapping(value = "/uploadAsynchronous", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R uploadAsynchronous(@RequestParam("file") MultipartFile file) {
        return R.ok().setData(aiUserResumeService.uploadResume(file, UserInfoUtils.getCurrentUserId()));
    }

}
