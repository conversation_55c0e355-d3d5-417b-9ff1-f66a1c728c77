package com.wisematch.modules.chat.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
@TableName("ai_job_position")
@Schema(description = "发布的岗位需求")
public class AiJobPosition {

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "岗位名称")
    private String position;

    @Schema(description = "官网链接")
    private String businessUrl;

//    @Schema(description = "智能体Id")
//    private String agentId;

    @Schema(description = "考核维度ID")
    private String jobExamineId;

    @Schema(description = "面试官Id")
    private String jobViewerId;

    @Schema(description = "展示图片")
    private String logo;

    @Schema(description = "岗位摘要")
    private String summary;

    @Schema(description = "关键标签")
    private String keyLabel;

    @Schema(description = "推荐奖金")
    private Integer recommendAward;

    @Schema(description = "岗位标签")
    private String labels;

    @Schema(description = "招聘类型，全职，兼职，必填")
    private String type;

    @Schema(description = "岗位信息")
    private String content;

    @Schema(description = "发布公司")
    private String company;

    @Schema(description = "学历要求")
    private String educational;

    @Schema(description = "最低工资")
    private Integer minPay;

    @Schema(description = "最高工资")
    private Integer maxPay;

    @Schema(description = "薪酬范围")
    private String payArea;

    @Schema(description = "工作方式")
    private String workType;

    @Schema(description = "职位类型")
    private String positionType;

    @Schema(description = "结算方式")
    private String positionPayType;

    @Schema(description = "行业类型")
    private String industryType;

    @Schema(description = "工作经验")
    private String workExperience;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Schema(description = "岗位状态（0停用，1已发布）")
    private Integer positionStatus;

    @Schema(description = "工作地点")
    private String workLocation;

    @Schema(description = "岗位福利")
    private String companyPositionBenefit;

    @Schema(description = "工作技能")
    private String companyPositionSkill;

    @Schema(description = "是否删除")
    private Integer isDel;

    @Schema(description = "创建用户Id")
    private String userId;

    @Schema(description = "组织机构Id")
    private String orgId;

    @Schema(description = "开场白")
    private String welcome;

    @Schema(description = "0未审核，1:审核通过，2审核拒绝")
    private Integer isVerify;

    @Schema(description = "是否在首页展示，0不展示，1:展示")
    private Integer isDisplayHome;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "是否热门")
    private Integer isHot;

    @Schema(description = "是否")
    private Integer isNewest;


    public AiJobPosition wrapAiJobPosition(AiJobPosition aiJobPosition) {

        if("全职".equals(aiJobPosition.getType())){
            if (null != aiJobPosition.getMinPay() && null != aiJobPosition.getMaxPay()) {
                String area = setPayArea(aiJobPosition.getMinPay(), aiJobPosition.getMaxPay());
                aiJobPosition.setPayArea(area);
            }
        }else {
            if (null != aiJobPosition.getMinPay() && null != aiJobPosition.getMaxPay()) {
                if("每小时".equals(aiJobPosition.getPositionPayType())){
                    String area = aiJobPosition.getMinPay() + "-" + aiJobPosition.getMaxPay()+"";
                    aiJobPosition.setPayArea(area);
                }else if("每日".equals(aiJobPosition.getPositionPayType())){
                    String area = aiJobPosition.getMinPay() + "-" + aiJobPosition.getMaxPay()+"";
                    aiJobPosition.setPayArea(area);
                }
            }
        }

        aiJobPosition.setKeyLabel(aiJobPosition.getPayArea());
        List<String> labels = new ArrayList<>();
        if (Objects.nonNull(aiJobPosition.getType())) {
            labels.add(aiJobPosition.getType());
        }
        if (Objects.nonNull(aiJobPosition.getWorkExperience())) {
            labels.add(aiJobPosition.getWorkExperience());
        }
        if (Objects.nonNull(aiJobPosition.getEducational())) {
            labels.add(aiJobPosition.getEducational());
        }
        aiJobPosition.setLabels(labels.stream().collect(Collectors.joining("|")));
        return aiJobPosition;
    }


    public static String setPayArea(Integer minPay, Integer maxPay) {
        return (minPay / 1000) + "-" + (maxPay / 1000) + "K";
    }

}
