package com.wisematch.modules.chat.convertor;

import com.wisematch.modules.chat.model.CommentMsg;
import com.wisematch.modules.chat.model.ShortMessage;
import com.wisematch.modules.chat.model.ShortMessageReportVO;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Collections;
import java.util.stream.Collectors;

public class ShortMessageReportVOConvertor {

    public static List<ShortMessageReportVO> convert(List<ShortMessage> shortMessages, List<CommentMsg> commentMsgs) {
        // 处理空集合情况
        if (CollectionUtils.isEmpty(shortMessages)) {
            return List.of();
        }

        return shortMessages.stream()
                .map(message -> {
                    ShortMessageReportVO reportVO = new ShortMessageReportVO();
                    // 复制基础属性
                    reportVO.setContent(message.getContent());
                    reportVO.setRole(message.getRole());
                    reportVO.setSecond(message.getSecond());

                    // 使用Stream查找匹配的CommentMsg并设置underlineDetail
                    if (!CollectionUtils.isEmpty(commentMsgs)) {
                        commentMsgs.stream()
                                // 过滤出underline与content完全匹配的CommentMsg
                                .filter(comment -> Objects.equals(message.getContent(), comment.getUnderline()))
                                // 取第一个匹配项
                                .findFirst()
                                // 如果存在则设置underlineDetail，否则设置为空列表
                                .ifPresentOrElse(
                                        matchedComment -> reportVO.setUnderlineDetail(
                                                CollectionUtils.isEmpty(matchedComment.getUnderlineDetail()) ?
                                                        Collections.singletonList(matchedComment.getUnderline()) :
                                                        matchedComment.getUnderlineDetail()),
                                        () -> reportVO.setUnderlineDetail(Collections.emptyList())
                                );
                    } else {
                        // 如果commentMsgs为空，直接设置空列表
                        reportVO.setUnderlineDetail(Collections.emptyList());
                    }

                    return reportVO;
                })
                .collect(Collectors.toList());
    }
}
