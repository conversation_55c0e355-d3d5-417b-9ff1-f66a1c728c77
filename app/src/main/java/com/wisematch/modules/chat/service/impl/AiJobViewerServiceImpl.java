package com.wisematch.modules.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.exception.RRException;
import com.wisematch.common.exception.RRExceptionEnum;
import com.wisematch.common.utils.PageUtils;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.entity.AiJobViewer;
import com.wisematch.modules.chat.enums.WiserConstant;
import com.wisematch.modules.chat.mapper.AiJobViewerMapper;
import com.wisematch.modules.chat.model.AiJobViewerQueryDTO;
import com.wisematch.modules.chat.service.AiJobViewerService;
import com.wisematch.modules.sys.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 智能体池
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025年7月10日 上午9:46:09
 */

@Service
@Slf4j
public class AiJobViewerServiceImpl extends ServiceImpl<AiJobViewerMapper, AiJobViewer>
        implements AiJobViewerService {

    @Override
    public Page<AiJobViewer> pageQuery(AiJobViewerQueryDTO dto) {

        QueryWrapper<AiJobViewer> wrapper = new QueryWrapper<>();

        if (StringUtils.isNotBlank(dto.getName())) {
            wrapper.lambda().like(AiJobViewer::getName, dto.getName());
        }
        if (StringUtils.isNotBlank(dto.getStatus())) {
            wrapper.lambda().eq(AiJobViewer::getStatus, dto.getStatus());
        }
        if (StringUtils.isNotBlank(dto.getOrgId())) {
            wrapper.lambda().eq(AiJobViewer::getOrgId, dto.getOrgId());
        }
        wrapper.lambda().and(i -> i.eq(AiJobViewer::getUserId, UserInfoUtils.getCurrentUserId())
                .or().eq(AiJobViewer::getBizScene, WiserConstant.DEFAULT)
        );
        wrapper.lambda().eq(AiJobViewer::getIsDel, WiserConstant.NOT_DELETE);
        return PageUtils.doPage(this, dto, wrapper);
    }

    @Override
    public void batchLogicDelete(List<String> ids) {
        this.baseMapper.batchLogicDelete(ids);
    }

    @Override
    public void logicDelete(String ids) {
        this.baseMapper.update(new UpdateWrapper<AiJobViewer>().lambda().eq(AiJobViewer::getId, ids)
                .set(AiJobViewer::getIsDel, WiserConstant.DELETED));
    }


    @Override
    public List<AiJobViewer> listAll(AiJobViewerQueryDTO dto) {
        QueryWrapper<AiJobViewer> wrapper = new QueryWrapper<>();

        if (StringUtils.isNotBlank(dto.getName())) {
            wrapper.lambda().like(AiJobViewer::getName, dto.getName());
        }
        if (StringUtils.isNotBlank(dto.getStatus())) {
            wrapper.lambda().eq(AiJobViewer::getStatus, dto.getStatus());
        }
        wrapper.lambda().eq(AiJobViewer::getIsDel, WiserConstant.NOT_DELETE);
        wrapper.lambda().and(i -> i.eq(AiJobViewer::getUserId, UserInfoUtils.getCurrentUserId())
                .or().eq(AiJobViewer::getBizScene, WiserConstant.DEFAULT)
        );
        return this.list(wrapper);
    }

    @Override
    public List<String> defaultPhoto() {
        List<AiJobViewer> aiJobViewers = this.baseMapper.selectList(new QueryWrapper<AiJobViewer>().lambda()
                .eq(AiJobViewer::getBizScene, WiserConstant.DEFAULT)
                .or()
                .eq(AiJobViewer::getUserId, UserInfoUtils.getCurrentUserId()));

        return aiJobViewers.stream().map(AiJobViewer::getPhoto).distinct().toList();
    }

    @Override
    public void createObj(AiJobViewer viewer) {

        String orgId = UserInfoUtils.getLoginUser().getOrgId();
        if(StringUtils.isBlank(orgId)){
            throw new RRException(RRExceptionEnum.ORG_NOT_VERIFY);
        }
        LoginUser loginUser = UserInfoUtils.getLoginUser();
        viewer.setUserId(loginUser.getUserId());
        viewer.setOrgId(loginUser.getOrgId());
        this.save(viewer);
    }
}
