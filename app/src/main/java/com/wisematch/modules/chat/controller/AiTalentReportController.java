package com.wisematch.modules.chat.controller;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.wisematch.common.annotation.SysLog;
import com.wisematch.common.lock.NotDoubleSubmit;
import com.wisematch.common.utils.R;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.entity.AiViewRecord;
import com.wisematch.modules.chat.service.AiViewRecordService;
import com.wisematch.modules.chat.service.AiViewReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version AiMatcherController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/reporter")
@Tag(name = "报告画像", description = "报告画像")
public class AiTalentReportController {
    @Autowired
    private AiViewReportService aiViewReportService;
    @Autowired
    private AiViewRecordService aiViewRecordService;

    @Operation(summary = "(wiser)获取单次面试报告")
    @GetMapping("/report")
    @NotDoubleSubmit
    @SysLog("获取单次面试报告")
    public R report(@RequestParam("chatId") String chatId) {
        AiViewRecord aiViewRecord = aiViewRecordService.getByRoomId(chatId);
        if(Objects.nonNull(aiViewRecord) && StringUtils.isNotBlank(aiViewRecord.getUserId())){
            if(UserInfoUtils.getCurrentUserId().equals(aiViewRecord.getUserId())){
                return R.ok().setData(aiViewReportService.report(aiViewRecord));
            }
        }
        return R.error();
    }

    @Operation(summary = "(matcher)获取单次面试报告")
    @GetMapping("/reportMatcher")
    @NotDoubleSubmit
    @SysLog("获取单次面试报告")
    public R reportWiser(@RequestParam("chatId") String chatId) {
        AiViewRecord aiViewRecord = aiViewRecordService.getByRoomId(chatId);
        if(Objects.nonNull(aiViewRecord) && StringUtils.isNotBlank(aiViewRecord.getUserId())){
            if(UserInfoUtils.getCurrentUserId().equals(aiViewRecord.getUserId())){
                return R.ok().setData(aiViewReportService.report(aiViewRecord));
            }
        }
        return R.ok().setData(aiViewReportService.report(aiViewRecord));
    }

}
