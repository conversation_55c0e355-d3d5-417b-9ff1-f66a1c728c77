package com.wisematch.modules.chat.model;

import com.wisematch.modules.common.utils.JsonUtils;
import com.wisematch.modules.exam.entity.AiExamUser;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version mbtiInfo.java, v0.1 2025-08-06 21:42
 */
@Data
public class MbtiInfo {

    String mbti;

    String name;

    String comments;

    List<Map<String, Double>> list;

    public MbtiInfoMatchDTO toMatchDTO(){
        MbtiInfoMatchDTO matchDTO = new MbtiInfoMatchDTO();
        matchDTO.setMbti(this.mbti);
        matchDTO.setList(this.list);
        return matchDTO;
    }

    public static MbtiInfo convertMbti(AiExamUser aiExamUser){
        MbtiInfo mbtiInfo = new MbtiInfo();
        if(Objects.nonNull(aiExamUser) && StringUtils.isNotBlank(aiExamUser.getReportContent())) {
            mbtiInfo = JsonUtils.fromJson(aiExamUser.getReportContent(), MbtiInfo.class);
        }
        return mbtiInfo;
    }

    public static MbtiInfo mock(){
        MbtiInfo mbtiInfo = new MbtiInfo();
        mbtiInfo.setMbti("ISTJ");
        mbtiInfo.setName("物流师型人格");
        mbtiInfo.setComments("候选人正直诚实、细心负责、务实可靠、乐于奉献，倾向于有计划，有系统，刻苦深入，是总能将任务按计划完成的“规划者和检查员”。这种人格类型的人具有强烈的职业道德和责任感。他们重视工作，注重细节，做事一丝不苟，会毫不吝啬时间和精力，耐心准确地完成每个任务。他们讲求逻辑时效，行动迅速，喜欢每推出一个项目就跟进直至完成。他们尊重结构和传统，经常被提供清晰等级和期望的组织、工作场所和教育环境所吸引");
        List<Map<String, Double>> list = List.of(Map.of("E",30.0,"I",70.0),
                Map.of("S",80.0,"N",20.0),
                Map.of("T",60.0,"F",40.0),
                Map.of("J",65.0,"P",35.0));
        mbtiInfo.setList(list);
        return mbtiInfo;
    }

}
