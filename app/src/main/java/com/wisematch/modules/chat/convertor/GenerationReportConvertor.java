package com.wisematch.modules.chat.convertor;

import com.wisematch.modules.chat.model.AgentsReportVO;
import com.wisematch.modules.chat.model.GenerationReport;
import com.wisematch.modules.chat.model.CommentMsg;

import java.util.*;
import java.util.stream.Collectors;

public class GenerationReportConvertor {

    /**
     * 使用Stream API将AgentsReportVO转换为GenerationReport
     * @param report 源对象
     * @return 转换后的目标对象
     */
    public static GenerationReport convert(AgentsReportVO report) {
        GenerationReport targetReport = new GenerationReport();
        if (report == null) {
            targetReport.setDimensions(Collections.emptyList());
            return targetReport;
        }

        List<CommentMsg> commentMsgs = report.getDimensions() == null
                ? Collections.emptyList()
                : report.getDimensions().stream()
                .map(GenerationReportConvertor::mapToCommentMsg)
                .collect(Collectors.toList());

        targetReport.setDimensions(commentMsgs);
        return targetReport;
    }

    /**
     * 将Dimension对象映射为CommentMsg对象
     * @param dimension 源维度对象
     * @return 转换后的评论消息对象
     */
    private static CommentMsg mapToCommentMsg(AgentsReportVO.Dimension dimension) {
        if (dimension == null) {
            return new CommentMsg();
        }

        CommentMsg commentMsg = new CommentMsg();
        commentMsg.setLabels(dimension.getLabels());
        commentMsg.setCheckPoint(dimension.getCheckPoint());
        commentMsg.setUnderline(dimension.getUnderline());
        List<String> underlineDetail = dimension.getUnderlineDetail();
        commentMsg.setUnderlineDetail(
                Optional.ofNullable(underlineDetail)
                        .filter(detail -> !detail.isEmpty())
                        .map(detail -> removeIncludedStrings(dimension.getUnderlineDetail()))
                        .orElseGet(() -> Collections.singletonList(dimension.getUnderline()))
        );
        commentMsg.setSummary(dimension.getSummary());
        commentMsg.setComments(dimension.getComments());
        commentMsg.setScore(dimension.getScore());
        commentMsg.setMsgIndex(null);

        return commentMsg;
    }

    /**
     * 移除列表中被其他元素包含的字符串
     * 1. 按长度从长到短排序（倒序）
     * 2. 只保留不被任何更长字符串包含的元素
     * @param list 原始字符串列表
     * @return 处理后的字符串列表
     */
    private static List<String> removeIncludedStrings(List<String> list) {
        if (list == null || list.size() <= 1) {
            return list;
        }

        // 1. 过滤null值并按长度倒序排序（从长到短）
        List<String> filteredAndSorted = list.stream()
                .filter(str -> str != null && !str.isEmpty()) // 同时过滤空字符串
                .sorted(Comparator.comparingInt(String::length).reversed()) // 明确的倒序排序
                .toList();

        // 2. 使用Stream收集不被任何已保留的更长字符串包含的元素
        return filteredAndSorted.stream()
                .reduce(new ArrayList<>(),
                        (resultList, currentStr) -> {
                            // 检查当前字符串是否被结果列表中任何已存在的字符串包含
                            // 因为已排序，结果列表中的字符串都比当前字符串长或等长
                            boolean isIncluded = resultList.stream()
                                    .anyMatch(includedStr -> includedStr.contains(currentStr));

                            if (!isIncluded) {
                                resultList.add(currentStr);
                            }
                            return resultList;
                        },
                        (list1, list2) -> {
                            list1.addAll(list2);
                            return list1;
                        });
    }
}
