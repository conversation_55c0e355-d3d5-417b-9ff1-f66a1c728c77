package com.wisematch.modules.chat.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wisematch.common.utils.*;
import com.wisematch.modules.chat.entity.AiNotifyMessage;
import com.wisematch.modules.chat.entity.AiOrganization;
import com.wisematch.modules.chat.enums.*;
import com.wisematch.modules.chat.factories.notifyMessage.ApplyMessagesDTO;
import com.wisematch.modules.chat.factories.notifyMessage.NotifyMessageFactories;
import com.wisematch.modules.chat.mapper.AiNotifyMessageMapper;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.chat.service.AiNotifyMessageService;
import com.wisematch.modules.chat.service.AiOrganizationService;
import com.wisematch.modules.sys.entity.SysUser;
import com.wisematch.modules.sys.model.PrivacyProtectionChangeDTO;
import com.wisematch.modules.sys.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.wisematch.modules.chat.enums.WiserConstant.NOT_DELETE;

@Slf4j
@Service
public class AiNotifyMessageServiceImpl extends ServiceImpl<AiNotifyMessageMapper, AiNotifyMessage> implements AiNotifyMessageService {

    @Autowired
    SysUserService sysUserService;

    @Autowired
    AiOrganizationService aiOrganizationService;
    @Override
    public Page<AiNotifyMessageVO> getByUserId(AiNotifyMessagePageDTO dto) {
        AiNotifyMessageType.containsExam(dto.getBizSence());

        LambdaQueryWrapper<AiNotifyMessage> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AiNotifyMessage::getIsDel, NOT_DELETE);

        wrapper.and(e->{e.eq(AiNotifyMessage::getReceiverId, UserInfoUtils.getCurrentUserId())
                .or().eq(AiNotifyMessage::getSenderId, UserInfoUtils.getCurrentUserId());});


        wrapper.and(e->{e.eq(AiNotifyMessage::getReceiverId, UserInfoUtils.getCurrentUserId())
                .or().eq(AiNotifyMessage::getSenderId, UserInfoUtils.getCurrentUserId());});

        wrapper.eq(StringUtils.isNotBlank(dto.getBizSence()), AiNotifyMessage::getBizSence, dto.getBizSence());
        wrapper.orderByDesc(AiNotifyMessage::getCreateTime);

        Page<AiNotifyMessage> aiNotifyMessagePage = PageUtils.doPage(this, dto, wrapper);

        List<AiNotifyMessage> aiNotifyMessages = aiNotifyMessagePage.getRecords();
        if(LoginUserType.MATCHER.name().equals(dto.getLoginUserType())){
            aiNotifyMessages.removeIf(msg -> msg.getBizSence().equals(CardType.TALENT.name()));
            aiNotifyMessages.removeIf(msg -> msg.getBizSence().equals(CardType.PORTRAIT.name()));
        }

        return PageConvertUtils.convert(
                aiNotifyMessagePage,
                AiNotifyMessageVO.class,
                (src, dest) -> {
                    AiNotifyMessageVO.setEnumAttribute(dest);
                }
        );
    }

    @Override
    public boolean notifyUser(AiNotifyMessage aiNotifyMessage) {
        ThreadPoolUtil.supplyAsync(() -> this
                .save(aiNotifyMessage)
        ).thenAccept(result -> {
            log.info("发送消息执行成功: {}", result);
        });
        return true;
    }


    @Override
    public AiNotifyMessageVO getMsgById(IdRequest id) {
        AiNotifyMessage aiNotifyMessage = this.getById(id.getId());
        return AiNotifyMessageVO.toVO(aiNotifyMessage);
    }

    @Override
    public AiNotifyMessageVO getStatusBizMsg(GetBizMsgDTO getBizMsgDTO) {

        AiNotifyMessage aiNotifyMessage = this.baseMapper.selectOne(new LambdaQueryWrapper<AiNotifyMessage>()
                .eq(AiNotifyMessage::getReceiverId, getBizMsgDTO.getReceiveId())
                .eq(AiNotifyMessage::getSenderId, getBizMsgDTO.getSenderId())
                .eq(AiNotifyMessage::getBizSence, getBizMsgDTO.getBizSence())
                .eq(AiNotifyMessage::getBizStatus, getBizMsgDTO.getBizStatus())
                .eq(AiNotifyMessage::getIsDel, NOT_DELETE));
        return AiNotifyMessageVO.toVO(aiNotifyMessage);
    }

    @Override
    public AiNotifyMessageVO getBizMsg(GetBizMsgDTO getBizMsgDTO) {
        AiNotifyMessage aiNotifyMessage = this.baseMapper.selectOne(new LambdaQueryWrapper<AiNotifyMessage>()
                .eq(AiNotifyMessage::getReceiverId, getBizMsgDTO.getReceiveId())
                .eq(AiNotifyMessage::getSenderId, getBizMsgDTO.getSenderId())
                .eq(AiNotifyMessage::getBizSence, getBizMsgDTO.getBizSence())
                .eq(AiNotifyMessage::getIsDel, NOT_DELETE));
        return AiNotifyMessageVO.toVO(aiNotifyMessage);
    }

    @Override
    public void addApplyMessage(AddApplyMessageDTO dto) {
        ApplyMessagesDTO applyMessagesDTO = ApplyMessagesDTO.getApplyMessagesDTO(dto);
        applyMessagesDTO.setLogo(UserInfoUtils.getLoginUser().getUserPhoto());
        applyMessagesDTO.setTitle("用户" + UserInfoUtils.getUsername() + "向你发送了查看面试视屏的请求");
        applyMessagesDTO.setSenderId(UserInfoUtils.getCurrentUserId());
        AiNotifyMessage aiNotifyMessage = NotifyMessageFactories.applyMessages(applyMessagesDTO);
        this.notifyUser(aiNotifyMessage);
    }

    @Override
    public IsCommunicateVO notifyCommunicat(NotifyCommunicatDTO dto) {

        aiOrganizationService.examCertification(UserInfoUtils.getCurrentUserId());

        IsCommunicateVO communicate = this.isCommunicate(dto);
        if (communicate.getCode().equals(WiserConstant.SUCCESS_STRING)) {
            return exchange(dto);
        }
        return communicate;
    }

    private IsCommunicateVO exchange(NotifyCommunicatDTO dto) {
        AiNotifyMessage aiNotifyMessage = NotifyMessageFactories.applyMessages(dto);
        aiNotifyMessage.setSenderType(dto.getSenderType());
        communicatMsgAdd(aiNotifyMessage);
        this.save(aiNotifyMessage);
        return new IsCommunicateVO(WiserConstant.SUCCESS_STRING
                , aiNotifyMessage.getBizStatus(), aiNotifyMessage.getReplyMsg());
    }

    @Override
    public IsCommunicateVO isCommunicate(NotifyCommunicatDTO dto) {
        LambdaQueryWrapper<AiNotifyMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiNotifyMessage::getBizSence, dto.getBizSence());
        queryWrapper.eq(AiNotifyMessage::getReceiverId, dto.getReceiverId());
        queryWrapper.eq(AiNotifyMessage::getSourceId, dto.getSourceId());
        queryWrapper.eq(AiNotifyMessage::getSenderType, dto.getSenderType());
        AiNotifyMessage one = this.getOne(queryWrapper);
        if (null == one) {
//            SysUser sysUser = sysUserService.getById(dto.getReceiverId());

//            if (null == sysUser.getMobile()) {
//                throw new RRException(RRExceptionEnum.PHONE_NUMBER_NOT_FOND);
//            }
            return new IsCommunicateVO(WiserConstant.SUCCESS_STRING);
        }
        return new IsCommunicateVO(WiserConstant.ERROR_STRING, one.getBizStatus(), one.getReplyMsg());
    }

    @Override
    public Page<AiNotifyMessageVO> communicationList(NotifyCommunicatListDTO dto) {

        AiNotifyMessageType.containsExam(dto.getBizSence());

        LambdaQueryWrapper<AiNotifyMessage> wrapper = Wrappers.lambdaQuery();
        String receiverId = dto.getReceiverId();
        String userId = UserInfoUtils.getCurrentUserId();

        if (AiNotifyMessageType.OTHER.name().equals(dto.getBizSence())) {
            wrapper.in(AiNotifyMessage::getBizSence
                    , List.of(AiNotifyMessageType.APPLY_VIDEO.name()
                            , AiNotifyMessageType.VX_EXCHANGE.name(), AiNotifyMessageType.PHONE_EXCHANGE.name()));
        }else {
            wrapper.eq(AiNotifyMessage::getBizSence, dto.getBizSence());
        }

        wrapper.eq(AiNotifyMessage::getIsDel, NOT_DELETE);

        wrapper.and(w -> w.and(x -> x.eq(AiNotifyMessage::getSenderId, userId)
                        .eq(AiNotifyMessage::getReceiverId, receiverId))
                .or(x -> x.eq(AiNotifyMessage::getSenderId, receiverId)
                        .eq(AiNotifyMessage::getReceiverId, userId)));

        Page<AiNotifyMessage> aiNotifyMessagePage = PageUtils.doPage(this, dto, wrapper);

        userRead(aiNotifyMessagePage.getRecords());

        return PageConvertUtils.convert(
                aiNotifyMessagePage,
                AiNotifyMessageVO.class,
                (src, dest) -> {
                    AiNotifyMessageVO.setEnumAttribute(dest);
                }
        );
    }

    private void userRead(List<AiNotifyMessage> records){

        records.forEach(e -> {
            if (e.getMsgStatus().equals(MsgStatus.UNREAD)) {
                e.setMsgStatus(MsgStatus.READ);
            }
        });
        this.updateBatchById(records);
    }


    @Override
    public AiNotifyMessage communicationAction(NotifyCommunicatActionDTO dto) {
        AiNotifyMessage aiNotifyMessage = this.getById(dto.getId());
        if (UserInfoUtils.getCurrentUserId().equals(aiNotifyMessage.getReceiverId())) {
            SysUser sender = sysUserService.getById(aiNotifyMessage.getSenderId());

            AiNotifyMessage.replyMsg(aiNotifyMessage, dto , sender);

            this.updateById(aiNotifyMessage);
            return aiNotifyMessage;
        }
        return null;
    }

    @Override
    public void setReportVideoPrivate(JSONObject report, SysUser sysUser) {
        AiNotifyMessageVO bizMsg = this.getBizMsg(
                GetBizMsgDTO.getApplyPrivacy(sysUser.getUserId(), UserInfoUtils.getCurrentUserId(),
                        AiNotifyMessageType.APPLY_VIDEO.name()));
        if (null == bizMsg) {
            report.put("videoApplyStatus", NotifyMsgBizStatus.NOT_SUBMIT);
            report.put("videoUrl", null);
        } else {
            report.put("videoApplyStatus", bizMsg.getBizStatus());
            if (!NotifyMsgBizStatus.AGREE.equals(bizMsg.getBizStatus())) {
                report.put("videoUrl", null);
            }
        }
    }

    @Override
    public JSONObject setBriefPrivate(JSONObject report, SysUser sysUser) {
        if(null == report){
            report = new JSONObject();
        }
        AiNotifyMessageVO vxBizMsg = this.getBizMsg(
                GetBizMsgDTO.getApplyPrivacy(sysUser.getUserId(), UserInfoUtils.getCurrentUserId(), AiNotifyMessageType.VX_EXCHANGE.name()));
        GetBizMsgDTO.reportPutVxPrivacy(report, vxBizMsg);
        AiNotifyMessageVO phoneBizMsg = this.getBizMsg(
                GetBizMsgDTO.getApplyPrivacy(sysUser.getUserId(), UserInfoUtils.getCurrentUserId(), AiNotifyMessageType.PHONE_EXCHANGE.name()));
        GetBizMsgDTO.reportPutPhonePrivacy(report, phoneBizMsg);
        return report;
    }

    @Override
    public boolean checkNotified(String name, String userId, String position) {
        return this.baseMapper.selectCount(new LambdaQueryWrapper<AiNotifyMessage>()
                .eq(AiNotifyMessage::getReceiverId, userId)
                .eq(AiNotifyMessage::getBizSence, name)
                .eq(AiNotifyMessage::getPosition, position)) > 0;
    }

    private void communicatMsgAdd(AiNotifyMessage aiNotifyMessage) {

        SysUser receiver = sysUserService.getById(aiNotifyMessage.getReceiverId());
        PrivacyProtectionChangeDTO privacy = PrivacyProtectionChangeDTO
                .getByString(receiver.getPrivacyProtection());

        this.buildUserForInsert(aiNotifyMessage, receiver);
        AiNotifyMessage.buildMsgForInsert(aiNotifyMessage, privacy, receiver);
    }


    private void buildUserForInsert(AiNotifyMessage aiNotifyMessage, SysUser receiver){
        String photo = receiver.getPhoto();
        aiNotifyMessage.setReceiverPhoto(photo);

        String orgId;
        AiOrganization organization;
        if (CardType.MATCHER.name().equals(aiNotifyMessage.getSenderType())) {
            orgId = UserInfoUtils.getLoginUser().getOrgId();
            organization = aiOrganizationService.getById(orgId);
            aiNotifyMessage.setReceiverName(DesensitizationUtils.name(receiver.getName()));
            aiNotifyMessage.setSenderName(organization.getOrganizationName());
        }else {
            orgId = receiver.getOrgId();
            organization = aiOrganizationService.getById(orgId);
            aiNotifyMessage.setReceiverName(organization.getOrganizationName());
            aiNotifyMessage.setSenderName(DesensitizationUtils.name(UserInfoUtils.getUsername()));
        }
        aiNotifyMessage.setLogo(organization.getOrganizationLogo());
        aiNotifyMessage.setTitle(organization.getOrganizationName());
    }

}
