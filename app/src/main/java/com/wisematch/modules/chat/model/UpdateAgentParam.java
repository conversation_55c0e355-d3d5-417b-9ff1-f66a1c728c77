package com.wisematch.modules.chat.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 更新智能体参数
 * <AUTHOR>
 * @version ChatParams.java, v0.1 2025-06-17 18:19
 */
@Data
public class UpdateAgentParam {

    @JSONField(name = "AppId")
    String appId;

    /**
     * roomId
     */
    @Schema(description = "roomId")
    @JSONField(name = "RoomId")
    String roomId;

    /**
     * taskId
     */
    @Schema(description = "taskId")
    @JSONField(name = "TaskId")
    String taskId = "viewer_agent_task";


    /**
     * https://www.volcengine.com/docs/6348/1404671
     */
    @JSONField(name = "Command")
    String Command = "interrupt";

    @JSONField(name = "Message")
    String Message;

    @JSONField(name = "InterruptMode")
    Integer InterruptMode = 1;
}
