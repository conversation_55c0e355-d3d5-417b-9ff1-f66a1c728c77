package com.wisematch.modules.chat.agent;

import com.wisematch.modules.chat.entity.AiJobPosition;
import com.wisematch.modules.chat.entity.AiViewPortrait;
import com.wisematch.modules.chat.enums.ViewerAgentConstant;
import com.wisematch.modules.chat.model.MbtiInfo;
import com.wisematch.modules.chat.service.AiJobPositionService;
import com.wisematch.modules.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class MbtiJobMatchAgent {


    @Autowired
    private AgentFacade agentFacade;
    @Autowired
    private AiJobPositionService aiJobPositionService;
    /**
     * 简历JSON提取
     * @param mbti   jd
     * @return
     */
    public String matchResult(String mbti, String jd) {
        log.info("开始计算匹配度");
        String prompt = agentFacade.getPrompt(ViewerAgentConstant.MBTI_TO_JOB);
        prompt = prompt.replace("$mbti", mbti);
        prompt = prompt.replace("$jd", jd);
        AgentContext context = new AgentContext();
        context.setAgentCode(ViewerAgentConstant.MBTI_TO_JOB);
        context.setPrompt(prompt);
        context.setUserMsg("帮我计算一下岗位匹配度");
        return agentFacade.supply(context);
    }

    public MbtiInfo matchResult(AiViewPortrait aiViewPortrait, MbtiInfo latestReport){
        AiJobPosition position = aiJobPositionService.getById(aiViewPortrait.getPositionId());
        String comments = this.matchResult(JsonUtils
                .toJson(latestReport.toMatchDTO()), aiViewPortrait.getPosition()+"   "+position.getContent());
        latestReport.setComments(comments);
        return latestReport;
    }

}
