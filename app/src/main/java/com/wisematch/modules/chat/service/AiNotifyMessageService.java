package com.wisematch.modules.chat.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.modules.chat.entity.AiNotifyMessage;
import com.wisematch.modules.chat.model.*;
import com.wisematch.modules.sys.entity.SysUser;

public interface AiNotifyMessageService extends IService<AiNotifyMessage> {


    Page<AiNotifyMessageVO> getByUserId(AiNotifyMessagePageDTO dto);

    boolean notifyUser(AiNotifyMessage aiNotifyMessage);

    AiNotifyMessageVO getMsgById(IdRequest id);

    AiNotifyMessageVO getStatusBizMsg(GetBizMsgDTO getBizMsgDTO);

    AiNotifyMessageVO getBizMsg(GetBizMsgDTO getBizMsgDTO);

    void addApplyMessage(AddApplyMessageDTO dto);

    IsCommunicateVO isCommunicate(NotifyCommunicatDTO dto);

    IsCommunicateVO notifyCommunicat(NotifyCommunicatDTO dto);

    Page<AiNotifyMessageVO> communicationList(NotifyCommunicatListDTO dto);

    AiNotifyMessage communicationAction(NotifyCommunicatActionDTO dto);

    /**
     * 视频播放隐私设置
     * @param report
     * @param sysUser
     */
    void setReportVideoPrivate(JSONObject report, SysUser sysUser);

    /**
     * 基础信息隐私设置
     * @param report
     * @param sysUser
     * @return
     */
    JSONObject setBriefPrivate(JSONObject report, SysUser sysUser);

    boolean checkNotified(String name, String userId, String position);
}
