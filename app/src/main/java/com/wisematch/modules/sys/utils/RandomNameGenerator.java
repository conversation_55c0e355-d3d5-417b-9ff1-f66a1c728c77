package com.wisematch.modules.sys.utils;

import java.util.Random;

public class RandomNameGenerator {

    // 常见姓氏库（部分复姓也包含）
    private static final String[] FAMILY_NAMES = {
            "赵","钱","孙","李","周","吴","郑","王",
            "冯","陈","褚","卫","蒋","沈","韩","杨",
            "朱","秦","尤","许","何","吕","施","张",
            "孔","曹","严","华","金","魏","陶","姜",
            "戚","谢","邹","喻","柏","水","窦","章",
            "云","苏","潘","葛","范","彭","郎","鲁",
            "韦","昌","马","苗","凤","花","方","俞",
            "任","袁","柳","酆","鲍","史","唐","费",
            "廉","岑","薛","雷","贺","倪","汤","滕",
            "殷","罗","毕","郝","邬","安","常","乐",
            "于","时","傅","皮","卞","齐","康","伍",
            "余","元","卜","顾","孟","平","黄","和",
            "穆","萧","尹","姚","邵","湛","汪","祁",
            "毛","禹","狄","米","贝","明","臧","计",
            "伏","成","戴","谈","宋","茅","庞","熊",
            "纪","舒","屈","项","祝","董","梁","杜",
            "阮","蓝","闵","席","季","麻","强","贾",
            "路","娄","危","江","童","颜","郭","梅",
            "盛","林","刁","钟","徐","丘","骆","高",
            "夏","蔡","田","樊","胡","凌","霍","虞",
            "万","支","柯","昝","管","卢","莫","经",
            "房","裘","缪","干","解","应","宗","丁",
            "宣","邓","郁","单","杭","洪","包","诸",
            "左","石","崔","吉","钮","龚","程","嵇",
            "邢","滑","裴","陆","荣","翁","荀","羊",
            "於","惠","甄","麴","家","封","芮","羿",
            "储","靳","汲","邴","糜","松","井","段",
            "富","巫","乌","焦","巴","弓","牧","隗",
            "山","谷","车","侯","宓","蓬","全","郗",
            "班","仰","秋","仲","伊","宫","宁","仇",
            "栾","暴","甘","钭","厉","戎","祖","武",
            "符","刘","景","詹","束","龙","叶","幸",
            "司徒","欧阳","上官","夏侯","诸葛","东方",
            "尉迟","长孙","宇文","慕容","司马","南宫"
    };

    // 常见名字汉字库
    private static final String[] GIVEN_NAME_CHARS = {
            "伟","刚","勇","毅","俊","峰","强","军","平","保",
            "东","文","辉","力","明","永","健","世","广","志",
            "义","兴","良","海","山","仁","波","宁","贵","福",
            "生","龙","元","全","国","胜","学","祥","才","发",
            "武","新","利","清","飞","彬","富","顺","信","子",
            "杰","涛","昌","成","康","星","光","天","达","安",
            "岩","中","茂","进","林","有","坚","和","彪","博",
            "诚","先","敬","震","振","壮","会","思","群","豪",
            "心","邦","承","乐","绍","功","松","善","厚","庆",
            "磊","民","友","裕","河","哲","江","超","浩","亮",
            "政","谦","亨","奇","固","之","轮","翰","朗","伯",
            "宏","言","若","鸣","朋","斌","梁","栋","维","启",
            "克","伦","翔","旭","鹏","泽","晨","辰","士","以",
            "建","家","致","树","炎","德","行","时","泰","盛",
            "雄","琛","钧","冠","策","腾","楠","榕","风","航",
            "弘","秀","娟","英","华","慧","巧","美","娜","静",
            "淑","惠","珠","翠","雅","芝","玉","萍","红","月",
            "彩","春","兰","凤","洁","梅","琳","素","云","莲",
            "真","环","雪","荣","爱","妹","霞","香","妹","瑞",
            "凡","佳","嘉","琼","勤","珍","贞","莉","桂","娣",
            "叶","璧","璐","娅","琦","晶","妍","茜","秋","珊",
            "莎","锦","黛","青","倩","婷","姣","婉","娴","瑾",
            "颖","露","瑶","怡","婵","雁","蓓","纨","仪","荷",
            "丹","蓉","眉","君","琴","蕊","薇","菁","梦","岚",
            "苑","婕","馨","瑗","琰","韵","融","园","艺","咏"
    };

    private static final Random random = new Random();

    // 生成随机名字
    public static String getRandomName() {
        String familyName = FAMILY_NAMES[random.nextInt(FAMILY_NAMES.length)];
        int givenLength = random.nextBoolean() ? 1 : 2;

        StringBuilder givenName = new StringBuilder();
        for (int i = 0; i < givenLength; i++) {
            givenName.append(GIVEN_NAME_CHARS[random.nextInt(GIVEN_NAME_CHARS.length)]);
        }

        return familyName + givenName.toString();
    }

    public static void main(String[] args) {
        for (int i = 0; i < 20; i++) {
            System.out.println(getRandomName());
        }
    }
}
