package com.wisematch.modules.sys.controller;

import cn.hutool.core.util.StrUtil;
import com.wisematch.common.annotation.ResourceAuth;
import com.wisematch.common.annotation.SysLog;
import com.wisematch.common.base.AbstractController;
import com.wisematch.common.utils.R;
import com.wisematch.common.utils.UserInfoUtils;
import com.wisematch.modules.chat.service.AiViewPortraitService;
import com.wisematch.modules.sms.SmsVerifyCodeManager;
import com.wisematch.modules.sys.annotation.Anonymous;
import com.wisematch.modules.sys.entity.SysUser;
import com.wisematch.modules.sys.model.*;
import com.wisematch.modules.sys.service.SysMenuNewService;
import com.wisematch.modules.sys.service.SysUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.ExecutionException;

/**
 * 系统用户
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@RestController
@RequestMapping("/sys/user")
@AllArgsConstructor
@Tag(name = "系统用户", description = "系统用户")
@Anonymous
public class SysUserController extends AbstractController {

    private final SysUserService sysUserService;
    private final PasswordEncoder passwordEncoder;
    private final SysMenuNewService sysMenuNewService;
    @Autowired
    private SmsVerifyCodeManager smsVerifyCodeManager;
    @Autowired
    private AiViewPortraitService aiViewPortraitService;

    /**
     * 获取登录的用户信息和菜单信息
     */
    @GetMapping(value = "/sysInfo")
    @ResourceAuth(value = "获取登录的用户信息和菜单信息", module = "系统用户")
    @Operation(summary = "获取登录的用户信息和菜单信息")
    public R sysInfo() {
        return R.ok().setData(sysUserService.sysInfo());
    }

    /**
     * 修改登录用户密码
     */
    @SysLog("修改密码")
    @RequestMapping(value = "/password", method = RequestMethod.POST)
    @ResourceAuth(value = "修改密码", module = "系统用户")
    @Operation(summary = "修改密码")
    public R password(@RequestBody UpdatePasswordDTO updatePasswordDTO) {
        if (StrUtil.isBlankIfStr(updatePasswordDTO.getNewPassword())) {
            return R.error("新密码不为能空");
        }
        String  password = passwordEncoder.encode(updatePasswordDTO.getPassword());
        String newPassword = passwordEncoder.encode(updatePasswordDTO.getNewPassword());

        SysUser user = sysUserService.getById(getUserId());
        if (!passwordEncoder.matches(password, user.getPassword())) {
            return R.error("原密码不正确");
        }
        //更新密码
        sysUserService.updatePassword(getUserId(), password, newPassword);
        return R.ok();
    }

    /**
     * 修改登录用户密码
     */
    @SysLog("web端修改密码")
    @RequestMapping(value = "/updatePassword", method = RequestMethod.POST)
    @ResourceAuth(value = "web端修改密码", module = "系统用户")
    @Operation(summary = "web端修改密码")
    public R updatePassword(@RequestBody UpdatePasswordDTO updatePasswordDTO) {
        return sysUserService.webUpdatePassword(updatePasswordDTO);
    }

    /**
     * 实名认证
     */
    @SysLog("实名认证")
    @PostMapping(value = "/idVerify")
    @Operation(summary = "实名认证")
    @ResourceAuth(value = "实名认证", module = "实名认证")
    public R id2MetaVerify(@RequestBody Id2MetaVerifyDTO user) throws ExecutionException, InterruptedException {
        sysUserService.id2MetaVerify(user);
        return R.ok();
    }

    /**
     * 保存用户
     */
    @SysLog("保存用户")
    @PostMapping(value = "/save")
    @Operation(summary = "保存用户")
    @ResourceAuth(value = "保存用户", module = "系统用户")
    public R save(@RequestBody @Validated SysUser user) {
        user.setCreateUserId(getUserId());
        sysUserService.saveUserRole(user);
        return R.ok();
    }

    //登录   --   上传简历（删除已有简历）
    @SysLog("批量新增测试用户")
    @PostMapping(value = "/createTestWiserBatch")
    @Operation(summary = "批量新增测试用户")
    @ResourceAuth(value = "批量新增测试用户", module = "批量新增测试用户")
    public R  createTestWiserBatch(@RequestBody @Validated CreateTestWiserBatchDTO dto) {
        sysUserService.createTestWiserBatch(dto);
        return R.ok();
    }


    /**
     * 修改用户
     */
    @SysLog("修改用户")
    @PostMapping(value = "/update")
    @Deprecated
    @Operation(summary = "修改用户")
    @ResourceAuth(value = "修改用户", module = "系统用户")
    public R update(@RequestBody @Validated SysUser user) {
        sysUserService.updateUserRole(user);
        return R.ok();
    }

    @SysLog("更换手机号")
    @PostMapping(value = "/modifyPhone")
    @Operation(summary = "更换手机号")
    @ResourceAuth(value = "更换手机号", module = "系统用户")
    public R webModifyUserInfo(@RequestBody @Validated ModifyPhoneDTO dto) {
        sysUserService.modifyPhone(dto);
        return R.ok();
    }

    @SysLog("修改个人信息")
    @PostMapping(value = "/modifyUserInfo")
    @Operation(summary = "修改个人信息")
    @ResourceAuth(value = "修改个人信息", module = "系统用户")
    public R modifyUserInfo(@RequestBody @Validated ModifyUserInfoDTO user) {
        sysUserService.updateUserInfo(user);
        return R.ok();
    }

    @SysLog("修改找工作状态")
    @Operation(summary = "修改找工作状态")
    @PostMapping(value = "/lookingJobStatusChange")
    @ResourceAuth(value = "修改找工作状态", module = "系统用户")
    public R lookingJobStatusChange(@RequestBody @Validated LookingJobStatusChangeDTO params) {
        sysUserService.changeLookingStatus(UserInfoUtils.getCurrentUserId(),params.getLookingJob());
        return R.ok();
    }

    @SysLog("是否进行隐私保护")
    @Operation(summary = "是否进行隐私保护")
    @PostMapping(value = "/privacyProtectionChange")
    @ResourceAuth(value = "是否进行隐私保护", module = "系统用户")
    public R privacyProtectionChange(@RequestBody @Validated PrivacyProtectionChangeDTO params) {
        aiViewPortraitService.privacyProtectionChange(params);
        return R.ok();
    }

    /**
     * 注销用户
     */
    @SysLog("注销用户")
    @GetMapping(value = "/cancelAccount")
    @Operation(summary = "注销用户")
    @ResourceAuth(value = "注销用户", module = "系统用户")
    public R cancelAccount() {
        return sysUserService.cancelAccount();
    }

    /**
     * 删除用户
     */
    @SysLog("删除用户")
    @PostMapping(value = "/delete")
    @Operation(summary = "删除用户")
    @ResourceAuth(value = "删除用户", module = "系统用户")
    public R deleteUser(@RequestBody SysUser user) {
        return sysUserService.deleteUser(user);
    }
}