package com.wisematch.modules.sys.controller;

import com.wisematch.modules.sys.annotation.Anonymous;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @version AiChatController.java, v0.1 2025-06-12 17:13
 */
@RestController
@RequestMapping("/health")
@Anonymous
public class HealthCheckController {

    @Resource
    private ThreadPoolExecutor agentsHttpExecutor;

    @GetMapping("/check")
    public Boolean check() {

        agentsHttpExecutor.execute(() -> {
            int i = 1 / 0;
        });

        return true;
    }

}
