package com.wisematch.modules.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wisematch.common.utils.R;
import com.wisematch.modules.chat.entity.AiOrganization;
import com.wisematch.modules.chat.model.AiOrganizationVerifyDTO;
import com.wisematch.modules.chat.model.UserInfo;
import com.wisematch.modules.sys.entity.SysUser;
import com.wisematch.modules.sys.model.*;
import com.wisematch.modules.sys.vo.RouterInfo;

import java.io.Serializable;
import java.util.concurrent.ExecutionException;


/**
 * 系统用户
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年9月18日 上午9:43:39
 */
public interface SysUserService extends IService<SysUser> {
    SysUser loadUserByUsername(String username);
    /**
     * 修改密码
     *
     * @param userId      用户ID
     * @param password    原密码
     * @param newPassword 新密码
     */
    int updatePassword(String userId, String password, String newPassword);

    //之前getById已经用于获取登录用户，获取不到自动重新登陆的业务。
    //当前的方法为普通业务方法
    SysUser getObjById(Serializable id);

    R webUpdatePassword(UpdatePasswordDTO updatePasswordDTO);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public SysUser selectUserByUserName(String userName);
    void saveUserRole(SysUser user);

    void updateUserRole(SysUser user);

    SysUser createUser(String phone);

    SysUser createUser(String phone, String password);

    SysUser createTestWiser(String phone, String password);

    void createTestWiserBatch(CreateTestWiserBatchDTO dto);

    UserInfo getUserByPhone(String phone);

    SysUser loadUserByPhone(String phone);

    void updateUserInfo(ModifyUserInfoDTO modifyUserInfoDTO);

    void modifyPhone(ModifyPhoneDTO modifyUserInfoDTO);

    void changeStatus(String userId, Integer status);


    void changeLookingStatus(String userId, Integer status);


    void retrieveAccount(String userId, Integer status);

    long idCardCount(String idCard);

    void id2MetaVerify(Id2MetaVerifyDTO user) throws ExecutionException, InterruptedException;

    void updateOrgId(String userId, String orgId);

    void updateRedisUserInfo(SysUser sysUser);

    public void updateOrgIdWithRedis(LoginUser loginUser, AiOrganizationVerifyDTO aiOrganizationVerifyDTO);

    public void cleanCompanyInfo(String userId);

    RouterInfo sysInfo();

    R cancelAccount();

    R deleteUser(SysUser sysUser);

    PrivacyProtectionChangeDTO getPrivacyProtection(String userId);

    public void updateOrgIdAndRedis(LoginUser loginUser, AiOrganization dto);
}
