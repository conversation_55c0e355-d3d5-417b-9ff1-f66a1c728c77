package com.wisematch.config.props;

import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
@Slf4j
public class ExecutorConfig {
    // 定义线程池关闭超时时间（秒）
    private static final int SHUTDOWN_TIMEOUT_SECONDS = 60;

    private ThreadPoolExecutor textExecutor;

    /**
     * 文本处理线程池，主线程池中调用，用同一个线程池可能会死锁
     */
    @Bean(value = "textExecutor")
    public ThreadPoolExecutor textExecutor() {
        // 直接创建并返回线程池，无需手动维护成员变量
        textExecutor = new ThreadPoolExecutor(
                3,                      // 核心线程数
                5,                      // 最大线程数
                60L,                    // 空闲线程存活时间
                TimeUnit.SECONDS,       // 时间单位
                new LinkedBlockingQueue<>(10000),  // 任务队列（容量10000）
                new ThreadPoolExecutor.AbortPolicy()  // 任务拒绝策略（超出容量时抛异常）
        );

        return textExecutor;
    }

    @PreDestroy
    public void destroy() {
        if (textExecutor != null && !textExecutor.isShutdown()) {
            try {
                log.warn("开始关闭textExecutor线程池，当前活跃线程数: {}", textExecutor.getActiveCount());

                textExecutor.shutdown();
                if (!textExecutor.awaitTermination(SHUTDOWN_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
                    log.warn("线程池{}秒内未完成所有任务，强制中断剩余任务", SHUTDOWN_TIMEOUT_SECONDS);
                    List<Runnable> unexecutedTasks = textExecutor.shutdownNow();
                    log.warn("强制中断后，未执行的任务数: {}", unexecutedTasks.size());

                    if (!textExecutor.awaitTermination(SHUTDOWN_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
                        log.error("textExecutor线程池强制关闭失败，可能存在未完成任务");
                    }
                }

                log.info("textExecutor线程池已关闭");
            } catch (InterruptedException e) {
                log.error("textExecutor线程池关闭过程被中断", e);
                Thread.currentThread().interrupt();
            }
        }
    }
}