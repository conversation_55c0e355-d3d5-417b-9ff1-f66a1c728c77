package com.wisematch.config;

import brave.Tracer;
import brave.propagation.TraceContext;
import com.wisematch.common.utils.ExceptionUtils;
import com.wisematch.modules.chat.service.DingTalkService;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;

// 创建支持Micrometer Tracing的装饰器
@Slf4j
public class TraceAwareThreadPoolExecutor extends ThreadPoolExecutor {
    private final Tracer tracer;

    private final DingTalkService dingTalkService;

    public TraceAwareThreadPoolExecutor(ThreadPoolExecutor executor, Tracer tracer, DingTalkService dingTalkService) {
        super(executor.getCorePoolSize(), executor.getMaximumPoolSize(),
                executor.getKeepAliveTime(TimeUnit.MILLISECONDS), TimeUnit.MILLISECONDS, executor.getQueue());
        this.tracer = tracer;
        this.dingTalkService = dingTalkService;
    }

    @Override
    public void execute(Runnable task) {
        super.execute(wrapWithTrace(task));
    }

    public Future<?> submit(Runnable task) {
        return super.submit(wrapWithTrace(task));
    }

    public <T> Future<T> submit(Callable<T> task) {
        return super.submit(wrapWithTrace(task));
    }

    public <T> Future<T> submit(Runnable task, T result) {
        return super.submit(wrapWithTrace(task), result);
    }

    private Runnable wrapWithTrace(Runnable task) {
        // 获取当前的trace context
        TraceContext.Builder builder = tracer.nextSpan().context().toBuilder();
        TraceContext traceContext = builder.build();

        return () -> {
            Tracer.SpanInScope ws = null;
            try {
                ws = tracer.withSpanInScope(tracer.toSpan(traceContext));
                task.run();
            } finally {
                if (ws != null) {
                    ws.close();
                }
            }
        };
    }

    private <T> Callable<T> wrapWithTrace(Callable<T> task) {
        TraceContext.Builder builder = tracer.nextSpan().context().toBuilder();
        TraceContext traceContext = builder.build();

        return () -> {
            Tracer.SpanInScope ws = null;
            try {
                ws = tracer.withSpanInScope(tracer.toSpan(traceContext));
                return task.call();
            } finally {
                if (ws != null) {
                    ws.close();
                }
            }
        };
    }

    @Override
    protected void afterExecute(Runnable r, Throwable t) {
        super.afterExecute(r, t);

        // 处理execute()提交的任务异常
        if (t != null) {
            log.error("线程池任务执行异常", t);
            dingTalkService.sendMsgToAlertWebHook(ExceptionUtils.getStackTraceAsString(t));
            return;
        }

        // 处理submit()提交的任务异常（封装在Future中）
        if (r instanceof Future<?> future) {
            try {
                if (future.isDone()) {
                    future.get();  // 触发ExecutionException
                }
            } catch (CancellationException e) {
                dingTalkService.sendMsgToAlertWebHook(ExceptionUtils.getStackTraceAsString(e));
                log.warn("任务被取消", e);
            } catch (ExecutionException e) {
                dingTalkService.sendMsgToAlertWebHook(ExceptionUtils.getStackTraceAsString(e));
                log.error("线程池任务执行异常", e.getCause());
            } catch (InterruptedException e) {
                dingTalkService.sendMsgToAlertWebHook(ExceptionUtils.getStackTraceAsString(e));
                log.error("任务被中断", e);
                Thread.currentThread().interrupt();
            }
        }
    }

}