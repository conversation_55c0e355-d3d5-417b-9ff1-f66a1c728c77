package com.wisematch.config.client;

import com.wisematch.common.exception.RRException;
import com.wisematch.common.utils.AgentsResult;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wisematch.modules.chat.service.DingTalkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.JdkClientHttpRequestFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.support.RestClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import org.springframework.web.util.DefaultUriBuilderFactory;

import java.net.http.HttpClient;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.List;
import java.util.Map;


@Slf4j
public class AgentsHttpClientServiceFactory {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static <T> T createHttpService(Class<T> serviceClass, String url, HttpClient httpClient,
                                          DefaultUriBuilderFactory.EncodingMode encodingMode,
                                          HttpHeaders headers, Duration timeout,
                                          List<? extends ClientHttpRequestInterceptor> clientHttpRequestInterceptors) {
        JdkClientHttpRequestFactory requestFactory = new JdkClientHttpRequestFactory(httpClient);
        requestFactory.setReadTimeout(timeout);

        DefaultUriBuilderFactory uriBuilderFactory;
        if (StringUtils.hasLength(url)) {
            uriBuilderFactory = new DefaultUriBuilderFactory(url);
        } else {
            uriBuilderFactory = new DefaultUriBuilderFactory();
        }

        if (encodingMode != null) {
            uriBuilderFactory.setEncodingMode(encodingMode);
        }

        RestClient.Builder clientBuilder = RestClient.builder()
                .baseUrl(url)
                .uriBuilderFactory(uriBuilderFactory)
                .requestFactory(requestFactory)
                .defaultStatusHandler(HttpStatusCode::isError, (request, response) -> {
                    String responseBody = new String(response.getBody().readAllBytes(), StandardCharsets.UTF_8);
                    log.error("receive error response: {}", responseBody);
                    // 尝试解析为 Result 对象
                    AgentsResult<?> result = objectMapper.readValue(responseBody, AgentsResult.class);
                    // 如果解析成功，直接抛出包含原始错误消息的异常
                    throw new RRException(result.getMessage());
                });

        if (!CollectionUtils.isEmpty(clientHttpRequestInterceptors)) {
            clientBuilder.requestInterceptors(interceptors -> interceptors.addAll(clientHttpRequestInterceptors));
        }

        if (!CollectionUtils.isEmpty(headers)) {
            clientBuilder.defaultHeaders(httpHeaders -> {
                for (Map.Entry<String, List<String>> entry : headers.entrySet()) {
                    httpHeaders.addAll(entry.getKey(), entry.getValue());
                }
            });
        }

        RestClient client = clientBuilder.build();
        RestClientAdapter restClientAdapter = RestClientAdapter.create(client);
        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builderFor(restClientAdapter).build();
        return factory.createClient(serviceClass);
    }

}
