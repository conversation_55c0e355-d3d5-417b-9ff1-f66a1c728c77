package com.wisematch.config.client;

import brave.Tracer;
import com.wisematch.config.TraceAwareThreadPoolExecutor;
import com.wisematch.modules.chat.service.DingTalkService;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;

import java.util.List;
import java.util.concurrent.*;

@Data
@Slf4j
public class ExecutorConfig {

    @Resource
    private Tracer tracer;

    @Resource
    private DingTalkService dingTalkService;

    private static final int SHUTDOWN_TIMEOUT_SECONDS = 60;

    private ThreadPoolExecutor agentsHttpExecutor;

    private ThreadPoolExecutor dingTalkHttpExecutor;

    @Bean
    ThreadPoolExecutor agentsHttpExecutor() {
        ThreadPoolExecutor rawExecutor = new ThreadPoolExecutor(5, 5, 60, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(5000), Executors.defaultThreadFactory(), new ThreadPoolExecutor.AbortPolicy());
        agentsHttpExecutor = new TraceAwareThreadPoolExecutor(rawExecutor, tracer, dingTalkService);
        return agentsHttpExecutor;
    }

    @Bean
    ThreadPoolExecutor dingTalkHttpExecutor() {
        ThreadPoolExecutor rawExecutor = new ThreadPoolExecutor(2, 2, 60, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(5000), Executors.defaultThreadFactory(), new ThreadPoolExecutor.AbortPolicy());
        dingTalkHttpExecutor = new TraceAwareThreadPoolExecutor(rawExecutor, tracer, dingTalkService);
        return dingTalkHttpExecutor;
    }

    @PreDestroy
    public void destroy() {
        shutdownExecutor(agentsHttpExecutor, "agentsHttpExecutor");
        shutdownExecutor(dingTalkHttpExecutor, "dingTalkHttpExecutor");
    }

    private void shutdownExecutor(ThreadPoolExecutor executor, String executorName) {
        if (executor != null && !executor.isShutdown()) {
            try {
                log.warn("开始关闭{}线程池，当前活跃线程数: {}", executorName, executor.getActiveCount());

                executor.shutdown();
                if (!executor.awaitTermination(SHUTDOWN_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
                    log.warn("线程池{}秒内未完成所有任务，强制中断剩余任务", SHUTDOWN_TIMEOUT_SECONDS);
                    List<Runnable> unexecutedTasks = executor.shutdownNow();
                    log.warn("强制中断后，未执行的任务数: {}", unexecutedTasks.size());

                    if (!executor.awaitTermination(SHUTDOWN_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
                        log.error("{}线程池强制关闭失败，可能存在未完成任务", executorName);
                    }
                }

                log.info("{}线程池已关闭", executorName);
            } catch (InterruptedException e) {
                log.error("{}线程池关闭过程被中断", executorName, e);
                Thread.currentThread().interrupt();
            }
        }
    }
}