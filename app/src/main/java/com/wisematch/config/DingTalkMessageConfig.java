package com.wisematch.config;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DingTalkMessageConfig {

    @Value("${ding-talk.url}")
    private String baseUrl;

    /**
     * 创建基础钉钉客户端Bean
     * 仅包含基础URL，不包含签名和令牌信息
     * 签名和令牌在实际发送消息时动态传入
     */
    @Bean
    public DingTalkClient dingTalkClient() {
        return new DefaultDingTalkClient(baseUrl);
    }
}
