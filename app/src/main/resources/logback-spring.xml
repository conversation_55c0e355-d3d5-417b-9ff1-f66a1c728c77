<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <logger name="org.springframework.web" level="INFO"/>
    <logger name="com.wisematch" level="INFO" />

    <!-- 定义日志文件的存储位置和名称 -->
    <property name="LOGS_PATH" value="./logs" />

    <!-- 控制台输出 -->
    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} - %5level [%thread] [%X{traceId}] [%X{spanId}] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 普通文件输出（INFO及以上级别） -->
    <appender name="File" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOGS_PATH}/app-default.log</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} - %5level [%thread] [%X{traceId}] [%X{spanId}] %logger{36} - %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOGS_PATH}/app-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>

    <!-- 错误日志单独输出（仅ERROR级别） -->
    <appender name="ErrorFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOGS_PATH}/app-error.log</file>
        <!-- 只处理ERROR级别日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} - %5level [%thread] [%X{traceId}] [%X{spanId}] %logger{36} - %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOGS_PATH}/app-error-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>

    <!-- 日志级别控制 -->
    <root level="info" >
        <appender-ref ref="Console" />
        <appender-ref ref="File" />
        <appender-ref ref="ErrorFile" /> <!-- 添加错误日志输出 -->
    </root>
</configuration>