<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>wise-match-ai</groupId>
    <artifactId>wise-match-ai</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <description>wise-match-ai</description>
    <modules>
        <module>app</module>
    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.5</version>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>
        <mybatis-plus.version>3.5.9</mybatis-plus.version>
        <mysql.version>8.0.33</mysql.version>
        <hutool.version>5.8.21</hutool.version>
        <velocity.version>1.7</velocity.version>
        <kaptcha.version>0.0.9</kaptcha.version>
        <qiniu.version>7.12.1</qiniu.version>
        <spring-ai.version>1.0.0</spring-ai.version>
        <jwt-version>0.11.2</jwt-version>
        <spring-ai-alibaba.version>*******</spring-ai-alibaba.version>
        <maven-deploy-plugin.version>3.1.1</maven-deploy-plugin.version>
        <flatten-maven-plugin.version>1.3.0</flatten-maven-plugin.version>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <!-- 子POM中出现的依赖版本 -->
        <dashscope-sdk-java.version>2.20.7</dashscope-sdk-java.version>
        <openai-java.version>2.12.0</openai-java.version>
        <tea-openapi.version>0.2.5</tea-openapi.version>
        <docmind_api.version>2.0.6</docmind_api.version>
        <fastjson.version>2.0.50</fastjson.version>
        <user-agent-utils.version>1.21</user-agent-utils.version>
        <commons-io.version>2.11.0</commons-io.version>
        <httpclient.version>4.5.13</httpclient.version>
        <easyexcel.version>3.3.2</easyexcel.version>
        <dytnsapi.version>2.1.0</dytnsapi.version>
        <spring-ai-alibaba-starter.version>1.0.0-M5.1</spring-ai-alibaba-starter.version>
        <xxl-job-core.version>3.2.0</xxl-job-core.version>
        <junit.version>3.8.1</junit.version>
        <alibabacloud-ocr_api.version>3.0.4</alibabacloud-ocr_api.version>
        <milvus-sdk-java.version>2.6.1</milvus-sdk-java.version>
        <volc-sdk-java.version>1.0.224</volc-sdk-java.version>
        <commons-text.version>1.10.0</commons-text.version>
        <commons-lang3.version>3.12.0</commons-lang3.version>
        <jedis.version>5.2.0</jedis.version>
        <apk-parser.version>2.6.5</apk-parser.version>
        <alibabacloud-cloudauth.version>2.0.13</alibabacloud-cloudauth.version>
        <aliyun-sdk-oss.version>3.18.3</aliyun-sdk-oss.version>
        <aliyun-java-sdk-core.version>4.6.4</aliyun-java-sdk-core.version>
        <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
        <springdoc-openapi.version>2.8.6</springdoc-openapi.version>
        <swagger-annotations.version>2.2.20</swagger-annotations.version>
        <dingtalk-message.version>2.0.0</dingtalk-message.version>
    </properties>

    <!-- 依赖管理：仅声明版本，不实际引入 -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibaba-dingtalk-service-sdk</artifactId>
                <version>${dingtalk-message.version}</version>
            </dependency>

            <!-- 阿里云OCR -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibabacloud-ocr_api20210707</artifactId>
                <version>${alibabacloud-ocr_api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>11.12.0</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-mysql</artifactId>
                <version>11.12.0</version>
            </dependency>

            <!-- Milvus -->
            <dependency>
                <groupId>io.milvus</groupId>
                <artifactId>milvus-sdk-java</artifactId>
                <version>${milvus-sdk-java.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibabacloud-cloudauth20221125</artifactId>
                <version>1.0.1</version>
            </dependency>
            <!-- 火山引擎SDK -->
            <dependency>
                <groupId>com.volcengine</groupId>
                <artifactId>volc-sdk-java</artifactId>
                <version>${volc-sdk-java.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.protobuf</groupId>
                        <artifactId>protobuf-java-util</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>error_prone_annotations</artifactId>
                        <groupId>com.google.errorprone</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-lang3</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- Apache工具类 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons-text.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-lang3</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>

            <!-- Redis客户端 -->
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>

            <!-- APK解析 -->
            <dependency>
                <groupId>net.dongliu</groupId>
                <artifactId>apk-parser</artifactId>
                <version>${apk-parser.version}</version>
            </dependency>

            <!-- MyBatis-Plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- MySQL驱动 -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>mysql</groupId>-->
<!--                <artifactId>mysql-connector-java</artifactId>-->
<!--                <version>8.0.23</version> &lt;!&ndash; 使用适合你项目的版本 &ndash;&gt;-->
<!--            </dependency>-->

            <!-- Velocity模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- Hutool工具类 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- 阿里云OSS -->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-sdk-oss.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>aliyun-java-sdk-core</artifactId>
                        <groupId>com.aliyun</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>httpclient</artifactId>
                        <groupId>org.apache.httpcomponents</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>aliyun-java-core</artifactId>
                        <groupId>com.aliyun</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>com.github.axet</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- 七牛云SDK -->
            <dependency>
                <groupId>com.qiniu</groupId>
                <artifactId>qiniu-java-sdk</artifactId>
                <version>${qiniu.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>okhttp</artifactId>
                        <groupId>com.squareup.okhttp3</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- Lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.30</version>
                <scope>provided</scope>
            </dependency>

            <!-- 阿里云实人认证 -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibabacloud-cloudauth20190307</artifactId>
                <version>${alibabacloud-cloudauth.version}</version>
            </dependency>
            <!-- Spring AI Alibaba -->
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-bom</artifactId>
                <version>${spring-ai-alibaba.version}</version>
                <type>pom</type>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-dashscope</artifactId>
                <version>${spring-ai-alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-memory</artifactId>
                <version>${spring-ai-alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-memory-redis</artifactId>
                <version>${spring-ai-alibaba.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>swagger-annotations</artifactId>
                        <groupId>io.swagger.core.v3</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>annotations</artifactId>
                        <groupId>org.jetbrains</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-memory-jdbc</artifactId>
                <version>${spring-ai-alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter</artifactId>
                <version>${spring-ai-alibaba-starter.version}</version>
            </dependency>

            <!-- 线程本地变量传递 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <!-- Spring AI -->
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-advisors-vector-store</artifactId>
                <version>${spring-ai.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>swagger-annotations</artifactId>
                        <groupId>io.swagger.core.v3</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- Swagger/OpenAPI -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc-openapi.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-lang3</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger-annotations.version}</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jwt-version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jwt-version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jwt-version}</version>
                <scope>runtime</scope>
            </dependency>

            <!-- 阿里云SDK核心 -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${aliyun-java-sdk-core.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>httpclient</artifactId>
                        <groupId>org.apache.httpcomponents</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-lang3</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 子POM中新增的依赖 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dashscope-sdk-java</artifactId>
                <version>${dashscope-sdk-java.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-simple</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.openai</groupId>
                <artifactId>openai-java</artifactId>
                <version>${openai-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>tea-openapi</artifactId>
                <version>${tea-openapi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>docmind_api20220711</artifactId>
                <version>${docmind_api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${user-agent-utils.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dytnsapi20200217</artifactId>
                <version>${dytnsapi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job-core.version}</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgument>-parameters</compilerArgument>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>aliyunmaven</id>
            <name>aliyun</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </repository>
        <repository>
            <id>sonatype</id>
            <name>OSS Sonatype</name>
            <url>https://oss.sonatype.org/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>